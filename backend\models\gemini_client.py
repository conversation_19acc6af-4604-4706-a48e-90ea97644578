import google.generativeai as genai
from typing import List, Dict, Optional
import asyncio
from ..utils.config import config

class GeminiClient:
    """Cliente para interação com a API Google Gemini"""

    def __init__(self):
        # Força usar a API Key correta
        self.api_key = "AIzaSyDASqL2UXOSEuXX0UTTOXzYciyoUwuk1mc"
        self.model_name = "gemini-1.5-flash"
        self._configure_api()
        self.model = None
        self._initialize_model()

    def _configure_api(self):
        """Configura a API do Gemini"""
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY não configurada")

        genai.configure(api_key=self.api_key)

    def _initialize_model(self):
        """Inicializa o modelo Gemini"""
        try:
            self.model = genai.GenerativeModel(self.model_name)
        except Exception as e:
            raise Exception(f"Erro ao inicializar modelo Gemini: {e}")

    async def generate_response(self, prompt: str, context: str = None,
                              conversation_history: List[Dict] = None) -> str:
        """
        Gera resposta usando o Gemini com contexto das notas

        Args:
            prompt: Pergunta do usuário
            context: Contexto relevante das notas do Obsidian
            conversation_history: Histórico da conversa

        Returns:
            Resposta gerada pelo Gemini
        """
        try:
            # Constrói o prompt completo
            full_prompt = self._build_prompt(prompt, context, conversation_history)

            # Gera resposta
            response = await asyncio.to_thread(
                self.model.generate_content, full_prompt
            )

            return response.text

        except Exception as e:
            return f"Erro ao gerar resposta: {str(e)}"

    def _build_prompt(self, user_prompt: str, context: str = None,
                     conversation_history: List[Dict] = None) -> str:
        """Constrói o prompt completo para o Gemini"""

        system_prompt = """Você é um assistente inteligente especializado em analisar e responder perguntas sobre notas pessoais do Obsidian.

INSTRUÇÕES:
1. Use APENAS as informações fornecidas no contexto das notas para responder
2. Se não houver informação suficiente no contexto, diga claramente que não encontrou informações relevantes
3. Seja preciso e cite trechos específicos das notas quando relevante
4. Mantenha um tom conversacional e útil
5. Se houver múltiplas notas relevantes, organize a resposta de forma clara
6. Lembre-se do contexto da conversa anterior quando relevante

"""

        # Adiciona contexto das notas
        if context:
            system_prompt += f"\nCONTEXTO DAS NOTAS:\n{context}\n"

        # Adiciona histórico da conversa
        if conversation_history:
            system_prompt += "\nHISTÓRICO DA CONVERSA:\n"
            for msg in conversation_history[-5:]:  # Últimas 5 mensagens
                role = "Usuário" if msg['role'] == 'user' else "Assistente"
                system_prompt += f"{role}: {msg['content']}\n"

        # Adiciona pergunta atual
        system_prompt += f"\nPERGUNTA ATUAL: {user_prompt}\n"
        system_prompt += "\nRESPOSTA:"

        return system_prompt

    async def generate_summary(self, text: str, max_length: int = 200) -> str:
        """Gera um resumo do texto fornecido"""
        try:
            prompt = f"""Resuma o seguinte texto em no máximo {max_length} caracteres,
            mantendo as informações mais importantes:

            {text}

            Resumo:"""

            response = await asyncio.to_thread(
                self.model.generate_content, prompt
            )

            return response.text.strip()

        except Exception as e:
            return f"Erro ao gerar resumo: {str(e)}"

    async def extract_keywords(self, text: str) -> List[str]:
        """Extrai palavras-chave do texto"""
        try:
            prompt = f"""Extraia as 10 palavras-chave mais importantes do seguinte texto.
            Retorne apenas as palavras separadas por vírgula:

            {text}

            Palavras-chave:"""

            response = await asyncio.to_thread(
                self.model.generate_content, prompt
            )

            keywords = [kw.strip() for kw in response.text.split(',')]
            return keywords[:10]  # Máximo 10 palavras-chave

        except Exception as e:
            return []

    async def categorize_content(self, text: str) -> str:
        """Categoriza o conteúdo do texto"""
        try:
            prompt = f"""Analise o seguinte texto e determine sua categoria principal.
            Escolha entre: Direito, Neurociência, Marketing, Tecnologia, Pessoal, Estudos, Trabalho, Outros.
            Retorne apenas o nome da categoria:

            {text}

            Categoria:"""

            response = await asyncio.to_thread(
                self.model.generate_content, prompt
            )

            return response.text.strip()

        except Exception as e:
            return "Outros"

# Instância global do cliente Gemini
gemini_client = GeminiClient()
