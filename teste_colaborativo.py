#!/usr/bin/env python3
"""
Teste do Sistema Colaborativo
Local + Gemini SEMPRE trabalhando juntos
"""

import asyncio
from obsidian_multi_agent_system import ObsidianMultiAgentSystem

async def teste_colaborativo():
    """Testa o sistema colaborativo com dados reais"""
    print("🤝 TESTE SISTEMA COLABORATIVO")
    print("=" * 40)
    print("Local + Gemini SEMPRE juntos!")
    print()
    
    # Inicializa sistema
    sistema = ObsidianMultiAgentSystem()
    
    # Adiciona um cofre de teste (se existir)
    cofre_teste = r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Direito Penal"
    
    print("📁 Tentando adicionar cofre de teste...")
    sucesso = sistema.add_vault("Direito Penal", cofre_teste, "Notas sobre direito penal")
    
    if sucesso:
        print("✅ Cofre adicionado! Indexando...")
        await sistema.index_all_vaults()
        print("✅ Indexação concluída!")
        
        # Testa perguntas colaborativas
        perguntas = [
            "O que é homicídio no direito penal?",
            "Quais são os crimes contra a vida?",
            "Explique o princípio da legalidade",
            "Sugira melhorias para organizar as notas de direito penal"
        ]
        
        for pergunta in perguntas:
            print(f"\n{'='*60}")
            print(f"❓ PERGUNTA: {pergunta}")
            print("=" * 60)
            
            # Sistema colaborativo em ação
            resposta = await sistema.chat(pergunta)
            print(resposta)
            
            print("\n⏸️ Pressione Enter para continuar...")
            input()
    
    else:
        print("⚠️ Cofre não encontrado. Testando sem cofres...")
        
        # Teste sem cofres
        pergunta = "Explique o que é direito penal"
        print(f"\n❓ PERGUNTA: {pergunta}")
        
        resposta = await sistema.chat(pergunta)
        print(resposta)

async def demo_rapida():
    """Demo rápida do sistema"""
    print("⚡ DEMO RÁPIDA - SISTEMA COLABORATIVO")
    print("=" * 45)
    
    sistema = ObsidianMultiAgentSystem()
    
    # Teste simples
    pergunta = "O que você pode fazer?"
    print(f"❓ {pergunta}")
    
    resposta = await sistema.chat(pergunta)
    print(f"\n🤖 {resposta}")

async def main():
    """Menu principal"""
    print("🤝 SISTEMA COLABORATIVO OBSIDIAN")
    print("=" * 35)
    print("Assistente Local + Gemini SEMPRE juntos!")
    print()
    
    print("Escolha:")
    print("1. 🧪 Teste completo")
    print("2. ⚡ Demo rápida")
    print("3. 📊 Ver estatísticas")
    
    try:
        choice = input("\nOpção: ").strip()
        
        if choice == "1":
            await teste_colaborativo()
        elif choice == "2":
            await demo_rapida()
        elif choice == "3":
            sistema = ObsidianMultiAgentSystem()
            stats = sistema.get_vault_stats()
            print(f"\n📊 Estatísticas:")
            print(f"   • Cofres: {stats['total_vaults']}")
            print(f"   • Arquivos: {stats['total_files']}")
        else:
            print("❌ Opção inválida")
            
    except KeyboardInterrupt:
        print("\n👋 Teste encerrado")

if __name__ == "__main__":
    asyncio.run(main())
