#!/usr/bin/env python3
"""
Script para instalar e configurar LLM local
Usando Ollama com modelos pequenos e rápidos
"""

import os
import sys
import subprocess
import requests
import time
from pathlib import Path

def print_header():
    """Imprime cabeçalho"""
    print("🤖 INSTALANDO LLM LOCAL")
    print("=" * 40)
    print("Vamos instalar Ollama + modelos pequenos")
    print("Para dar suporte ao sistema Obsidian")
    print()

def check_ollama_installed():
    """Verifica se Ollama está instalado"""
    try:
        result = subprocess.run(['ollama', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama já está instalado!")
            print(f"   Versão: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def download_ollama():
    """Baixa e instala Ollama"""
    print("📥 Baixando Ollama...")
    
    # URL do instalador do Ollama para Windows
    url = "https://ollama.com/download/windows"
    
    print(f"🌐 Acesse: {url}")
    print("📋 Instruções:")
    print("   1. Baixe o instalador do Ollama")
    print("   2. Execute o arquivo .exe")
    print("   3. Siga as instruções de instalação")
    print("   4. Reinicie o terminal após a instalação")
    print()
    
    input("⏸️ Pressione Enter após instalar o Ollama...")
    
    # Verifica se foi instalado
    if check_ollama_installed():
        return True
    else:
        print("❌ Ollama não foi detectado. Tente instalar manualmente.")
        return False

def start_ollama_service():
    """Inicia o serviço do Ollama"""
    print("🔄 Iniciando serviço Ollama...")
    
    try:
        # Inicia Ollama em background
        subprocess.Popen(['ollama', 'serve'], 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.DEVNULL)
        
        # Aguarda alguns segundos
        time.sleep(3)
        
        # Testa se está rodando
        try:
            response = requests.get('http://localhost:11434/api/tags', timeout=5)
            if response.status_code == 200:
                print("✅ Serviço Ollama iniciado com sucesso!")
                return True
        except:
            pass
        
        print("⚠️ Serviço pode não ter iniciado corretamente")
        return False
        
    except Exception as e:
        print(f"❌ Erro ao iniciar serviço: {e}")
        return False

def install_models():
    """Instala modelos pequenos e rápidos"""
    print("📦 Instalando modelos LLM...")
    
    # Modelos pequenos recomendados (em ordem de preferência)
    models = [
        ("llama3.2:1b", "Llama 3.2 1B - Muito rápido e pequeno"),
        ("phi3:mini", "Phi-3 Mini - Eficiente da Microsoft"),
        ("qwen2:0.5b", "Qwen2 0.5B - Ultra pequeno"),
        ("tinyllama", "TinyLlama - Menor modelo disponível")
    ]
    
    installed_models = []
    
    for model_name, description in models:
        print(f"\n🔄 Instalando {model_name}...")
        print(f"   {description}")
        
        try:
            # Comando para instalar modelo
            result = subprocess.run(['ollama', 'pull', model_name], 
                                  capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ {model_name} instalado com sucesso!")
                installed_models.append(model_name)
                break  # Instala apenas o primeiro que funcionar
            else:
                print(f"❌ Erro ao instalar {model_name}: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ Timeout ao instalar {model_name}")
        except Exception as e:
            print(f"❌ Erro: {e}")
    
    if installed_models:
        print(f"\n✅ Modelos instalados: {', '.join(installed_models)}")
        return installed_models[0]  # Retorna o primeiro modelo instalado
    else:
        print("\n❌ Nenhum modelo foi instalado com sucesso")
        return None

def test_local_llm(model_name):
    """Testa o LLM local"""
    print(f"\n🧪 Testando modelo {model_name}...")
    
    try:
        # Teste simples
        result = subprocess.run([
            'ollama', 'run', model_name, 
            'Responda em português: O que é direito penal?'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            response = result.stdout.strip()
            print("✅ Teste bem-sucedido!")
            print(f"🤖 Resposta: {response[:100]}...")
            return True
        else:
            print(f"❌ Erro no teste: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Timeout no teste")
        return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def create_local_llm_config(model_name):
    """Cria configuração para usar LLM local"""
    config = {
        "local_llm": {
            "enabled": True,
            "provider": "ollama",
            "model": model_name,
            "base_url": "http://localhost:11434",
            "temperature": 0.3,
            "max_tokens": 2000
        },
        "hybrid_mode": {
            "enabled": True,
            "primary": "gemini",
            "fallback": "local",
            "use_local_for": [
                "quick_analysis",
                "simple_questions",
                "backup_responses"
            ]
        }
    }
    
    import json
    with open("local_llm_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ Configuração salva em local_llm_config.json")

def install_python_dependencies():
    """Instala dependências Python para Ollama"""
    print("📦 Instalando dependências Python...")
    
    packages = [
        "ollama",
        "langchain-ollama",
        "httpx"
    ]
    
    for package in packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                          check=True, capture_output=True)
            print(f"✅ {package} instalado")
        except subprocess.CalledProcessError:
            print(f"❌ Erro ao instalar {package}")

def main():
    """Função principal"""
    print_header()
    
    # 1. Verifica se Ollama está instalado
    if not check_ollama_installed():
        print("📥 Ollama não encontrado. Vamos instalar...")
        if not download_ollama():
            print("❌ Falha na instalação do Ollama")
            return False
    
    # 2. Instala dependências Python
    install_python_dependencies()
    
    # 3. Inicia serviço
    if not start_ollama_service():
        print("⚠️ Problemas com o serviço. Continuando...")
    
    # 4. Instala modelos
    model_name = install_models()
    if not model_name:
        print("❌ Nenhum modelo instalado. Abortando...")
        return False
    
    # 5. Testa modelo
    if test_local_llm(model_name):
        print("✅ LLM local funcionando!")
    else:
        print("⚠️ Problemas no teste, mas modelo instalado")
    
    # 6. Cria configuração
    create_local_llm_config(model_name)
    
    # 7. Instruções finais
    print("\n" + "=" * 40)
    print("🎉 INSTALAÇÃO CONCLUÍDA!")
    print("=" * 40)
    print(f"✅ Modelo instalado: {model_name}")
    print("✅ Configuração criada")
    print("✅ Sistema híbrido configurado")
    print()
    print("💡 COMO USAR:")
    print("   • Gemini: Respostas principais")
    print("   • LLM Local: Backup e análises rápidas")
    print("   • Automático: Sistema escolhe o melhor")
    print()
    print("🚀 PRÓXIMOS PASSOS:")
    print("   1. Execute: python sistema_hibrido.py")
    print("   2. Ou use a interface web normalmente")
    print("   3. O sistema usará ambos automaticamente")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Instalação cancelada")
    except Exception as e:
        print(f"\n❌ Erro: {e}")
