# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyDummy_Key_For_Testing_Replace_With_Real_Key

# Obsidian Notes Configuration
OBSIDIAN_VAULT_PATH=./data/obsidian_notes

# Database Configuration
DATABASE_PATH=./data/memory.db
VECTOR_STORE_PATH=./data/vector_store

# Server Configuration
HOST=localhost
PORT=8000
DEBUG=True

# Chat Configuration
MAX_MEMORY_MESSAGES=50
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_TOKENS_PER_REQUEST=4000

# Embedding Model
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
