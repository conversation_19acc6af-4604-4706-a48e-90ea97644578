import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path
from .config import config

class DatabaseManager:
    """Gerenciador do banco de dados SQLite para memória do chat"""
    
    def __init__(self, db_path: Path = None):
        self.db_path = db_path or config.DATABASE_PATH
        self.init_database()
    
    def init_database(self):
        """Inicializa o banco de dados com as tabelas necessárias"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Tabela de conversas
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT UNIQUE NOT NULL,
                    title TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Tabela de mensagens
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conversation_id INTEGER,
                    role TEXT NOT NULL,
                    content TEXT NOT NULL,
                    metadata TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (conversation_id) REFERENCES conversations (id)
                )
            """)
            
            # Tabela de documentos indexados
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS indexed_documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_path TEXT UNIQUE NOT NULL,
                    file_hash TEXT NOT NULL,
                    title TEXT,
                    content_preview TEXT,
                    chunk_count INTEGER DEFAULT 0,
                    indexed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Tabela de contexto semântico
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS semantic_context (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    conversation_id INTEGER,
                    document_ids TEXT,
                    context_summary TEXT,
                    relevance_score REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (conversation_id) REFERENCES conversations (id)
                )
            """)
            
            conn.commit()
    
    def create_conversation(self, session_id: str, title: str = None) -> int:
        """Cria uma nova conversa"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO conversations (session_id, title)
                VALUES (?, ?)
            """, (session_id, title or f"Conversa {datetime.now().strftime('%d/%m/%Y %H:%M')}"))
            return cursor.lastrowid
    
    def add_message(self, conversation_id: int, role: str, content: str, metadata: Dict = None):
        """Adiciona uma mensagem à conversa"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO messages (conversation_id, role, content, metadata)
                VALUES (?, ?, ?, ?)
            """, (conversation_id, role, content, json.dumps(metadata or {})))
            
            # Atualiza timestamp da conversa
            cursor.execute("""
                UPDATE conversations 
                SET updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
            """, (conversation_id,))
    
    def get_conversation_history(self, session_id: str, limit: int = None) -> List[Dict]:
        """Recupera histórico de uma conversa"""
        limit = limit or config.MAX_MEMORY_MESSAGES
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT m.role, m.content, m.metadata, m.timestamp
                FROM messages m
                JOIN conversations c ON m.conversation_id = c.id
                WHERE c.session_id = ?
                ORDER BY m.timestamp DESC
                LIMIT ?
            """, (session_id, limit))
            
            messages = []
            for row in cursor.fetchall():
                messages.append({
                    'role': row[0],
                    'content': row[1],
                    'metadata': json.loads(row[2]) if row[2] else {},
                    'timestamp': row[3]
                })
            
            return list(reversed(messages))  # Ordem cronológica
    
    def get_conversation_id(self, session_id: str) -> Optional[int]:
        """Obtém ID da conversa pelo session_id"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id FROM conversations WHERE session_id = ?
            """, (session_id,))
            result = cursor.fetchone()
            return result[0] if result else None
    
    def add_indexed_document(self, file_path: str, file_hash: str, title: str, 
                           content_preview: str, chunk_count: int):
        """Registra um documento indexado"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO indexed_documents 
                (file_path, file_hash, title, content_preview, chunk_count, updated_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (file_path, file_hash, title, content_preview, chunk_count))
    
    def get_indexed_documents(self) -> List[Dict]:
        """Lista todos os documentos indexados"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT file_path, title, content_preview, chunk_count, indexed_at
                FROM indexed_documents
                ORDER BY updated_at DESC
            """)
            
            documents = []
            for row in cursor.fetchall():
                documents.append({
                    'file_path': row[0],
                    'title': row[1],
                    'content_preview': row[2],
                    'chunk_count': row[3],
                    'indexed_at': row[4]
                })
            
            return documents

# Instância global do gerenciador de banco
db_manager = DatabaseManager()
