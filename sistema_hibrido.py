#!/usr/bin/env python3
"""
Sistema Híbrido: Gemini + LLM Local
Combina o melhor dos dois mundos
"""

import asyncio
import json
import os
from pathlib import Path
from typing import Optional, Dict, Any

# Imports principais
from obsidian_multi_agent_system import ObsidianMultiAgentSystem
from langchain_google_genai import ChatGoogleGenerativeAI

# Configuração
GOOGLE_API_KEY = "AIzaSyCq1rBFLFqX04jQ2ejd5Tkbane-lTvIsVU"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

class HybridLLMSystem:
    """Sistema que combina Gemini + LLM Local"""
    
    def __init__(self):
        self.gemini_llm = None
        self.local_llm = None
        self.config = self.load_config()
        self.setup_llms()
        
        print("🤖 Sistema Híbrido inicializado!")
        print(f"✅ Gemini: {'Ativo' if self.gemini_llm else 'Inativo'}")
        print(f"✅ LLM Local: {'Ativo' if self.local_llm else 'Inativo'}")
    
    def load_config(self):
        """Carrega configuração do sistema híbrido"""
        config_file = "local_llm_config.json"
        
        if Path(config_file).exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # Configuração padrão
            return {
                "local_llm": {
                    "enabled": False,
                    "provider": "ollama",
                    "model": "llama3.2:1b",
                    "base_url": "http://localhost:11434"
                },
                "hybrid_mode": {
                    "enabled": True,
                    "primary": "gemini",
                    "fallback": "local"
                }
            }
    
    def setup_llms(self):
        """Configura os LLMs"""
        # Gemini (sempre ativo)
        try:
            self.gemini_llm = ChatGoogleGenerativeAI(
                model="gemini-1.5-pro",
                temperature=0.3,
                google_api_key=GOOGLE_API_KEY
            )
            print("✅ Gemini configurado")
        except Exception as e:
            print(f"❌ Erro no Gemini: {e}")
        
        # LLM Local (se disponível)
        if self.config["local_llm"]["enabled"]:
            try:
                self.setup_local_llm()
            except Exception as e:
                print(f"⚠️ LLM Local não disponível: {e}")
    
    def setup_local_llm(self):
        """Configura LLM local (Ollama)"""
        try:
            # Tenta importar Ollama
            from langchain_ollama import OllamaLLM
            
            self.local_llm = OllamaLLM(
                model=self.config["local_llm"]["model"],
                base_url=self.config["local_llm"]["base_url"],
                temperature=0.3
            )
            
            # Teste rápido
            response = self.local_llm.invoke("Teste")
            print("✅ LLM Local configurado")
            
        except ImportError:
            print("⚠️ langchain-ollama não instalado")
        except Exception as e:
            print(f"⚠️ LLM Local indisponível: {e}")
            self.local_llm = None
    
    async def choose_llm(self, query: str, context_size: int = 0) -> str:
        """Escolhe qual LLM usar baseado na query"""
        
        # Critérios para usar LLM local
        use_local_conditions = [
            len(query) < 100,  # Queries curtas
            context_size < 500,  # Contexto pequeno
            any(word in query.lower() for word in [
                "rápido", "simples", "básico", "resumo"
            ])
        ]
        
        # Se tem LLM local e atende critérios
        if self.local_llm and any(use_local_conditions):
            return "local"
        
        # Senão usa Gemini
        return "gemini"
    
    async def invoke_llm(self, llm_type: str, prompt: str) -> str:
        """Invoca o LLM especificado"""
        try:
            if llm_type == "local" and self.local_llm:
                response = self.local_llm.invoke(prompt)
                return f"🏠 [Local] {response}"
            
            elif llm_type == "gemini" and self.gemini_llm:
                from langchain.schema import HumanMessage
                response = await self.gemini_llm.ainvoke([HumanMessage(content=prompt)])
                return f"🌐 [Gemini] {response.content}"
            
            else:
                return "❌ LLM não disponível"
                
        except Exception as e:
            return f"❌ Erro no LLM {llm_type}: {e}"
    
    async def hybrid_chat(self, query: str, context: str = "") -> str:
        """Chat híbrido que escolhe o melhor LLM"""
        
        # Escolhe LLM
        chosen_llm = await self.choose_llm(query, len(context))
        
        # Monta prompt
        if context:
            prompt = f"Contexto: {context}\n\nPergunta: {query}\n\nResponda em português:"
        else:
            prompt = f"Pergunta: {query}\n\nResponda em português:"
        
        # Tenta LLM escolhido
        response = await self.invoke_llm(chosen_llm, prompt)
        
        # Se falhou, tenta fallback
        if response.startswith("❌") and chosen_llm != "gemini":
            print(f"⚠️ {chosen_llm} falhou, tentando Gemini...")
            response = await self.invoke_llm("gemini", prompt)
        
        return response

class ObsidianHybridSystem(ObsidianMultiAgentSystem):
    """Sistema Obsidian com LLM híbrido"""
    
    def __init__(self):
        # Inicializa sistema base
        super().__init__()
        
        # Adiciona sistema híbrido
        self.hybrid_llm = HybridLLMSystem()
        
        print("🔗 Sistema Obsidian Híbrido inicializado!")
    
    async def hybrid_chat(self, message: str) -> str:
        """Chat que usa sistema híbrido"""
        print(f"💬 Usuário: {message}")
        
        # Busca contexto relevante
        context = await self._get_relevant_context(message)
        context_text = "\n".join([ctx["content"] for ctx in context[:3]])
        
        # Usa sistema híbrido
        response = await self.hybrid_llm.hybrid_chat(message, context_text)
        
        return response

async def demo_sistema_hibrido():
    """Demonstração do sistema híbrido"""
    print("🎯 DEMO SISTEMA HÍBRIDO")
    print("=" * 30)
    
    # Inicializa sistema
    system = HybridLLMSystem()
    
    # Testes
    perguntas = [
        "O que é direito?",
        "Explique direito penal de forma simples",
        "Quais são os princípios constitucionais fundamentais?",
        "Resumo rápido sobre contratos"
    ]
    
    for pergunta in perguntas:
        print(f"\n❓ {pergunta}")
        resposta = await system.hybrid_chat(pergunta)
        print(f"🤖 {resposta[:150]}...")

async def demo_obsidian_hibrido():
    """Demo com cofres Obsidian"""
    print("\n🎯 DEMO OBSIDIAN HÍBRIDO")
    print("=" * 30)
    
    try:
        # Inicializa sistema Obsidian híbrido
        system = ObsidianHybridSystem()
        
        # Adiciona um cofre de teste
        caminho_teste = r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Direito Penal"
        
        if Path(caminho_teste).exists():
            system.add_vault("Direito Penal", caminho_teste, "Teste")
            await system.index_all_vaults()
            
            # Teste com contexto
            resposta = await system.hybrid_chat("Quais são os crimes contra a vida?")
            print(f"🤖 {resposta[:200]}...")
        else:
            print("⚠️ Cofre de teste não encontrado")
            
    except Exception as e:
        print(f"❌ Erro: {e}")

async def main():
    """Função principal"""
    print("🤖 SISTEMA HÍBRIDO OBSIDIAN")
    print("=" * 40)
    print("Gemini + LLM Local para máxima eficiência")
    print()
    
    # Menu
    print("Escolha uma opção:")
    print("1. 🧪 Demo sistema híbrido")
    print("2. 📁 Demo com cofres Obsidian")
    print("3. ⚙️ Instalar LLM local")
    print("4. 🌐 Usar interface web")
    
    try:
        choice = input("\nOpção (1-4): ").strip()
        
        if choice == "1":
            await demo_sistema_hibrido()
        elif choice == "2":
            await demo_obsidian_hibrido()
        elif choice == "3":
            print("🔄 Executando instalador...")
            os.system("python install_local_llm.py")
        elif choice == "4":
            print("🌐 Iniciando interface web...")
            os.system("streamlit run web_interface.py")
        else:
            print("❌ Opção inválida")
            
    except KeyboardInterrupt:
        print("\n👋 Programa encerrado")

if __name__ == "__main__":
    asyncio.run(main())
