#!/usr/bin/env python3
"""
Teste direto do chat sem usar o servidor
"""

import sys
import os
sys.path.append('.')

# Força reload das configurações
import importlib
if 'backend.utils.config' in sys.modules:
    importlib.reload(sys.modules['backend.utils.config'])

from backend.models.gemini_client import GeminiClient
from backend.services.search_service import search_service
from backend.utils.config import config

async def test_chat_direct():
    """Testa o chat diretamente"""
    
    print(f"🔑 API Key configurada: {config.GEMINI_API_KEY[:20]}...{config.GEMINI_API_KEY[-10:]}")
    print(f"🤖 Modelo Gemini: {config.GEMINI_MODEL}")
    
    try:
        # Cria cliente Gemini
        gemini = GeminiClient()
        
        # Testa busca
        print("🔍 Testando busca...")
        results = await search_service.semantic_search("direito constitucional", top_k=3)
        print(f"✅ Encontrados {len(results)} resultados")
        
        # Prepara contexto
        context = ""
        if results:
            context = f"CONTEXTO DAS NOTAS:\n{results[0]['chunk_content'][:500]}..."
        
        # Testa resposta
        print("💬 Testando resposta do Gemini...")
        response = await gemini.generate_response(
            prompt="Explique os princípios fundamentais do Direito Constitucional",
            context=context
        )
        
        print(f"✅ Resposta: {response[:200]}...")
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    
    print("🧪 TESTE DIRETO DO CHAT")
    print("=" * 40)
    
    # Força recarregar configuração
    os.environ['GEMINI_API_KEY'] = 'AIzaSyDASqL2UXOSEuXX0UTTOXzYciyoUwuk1mc'
    
    success = asyncio.run(test_chat_direct())
    
    if success:
        print("\n🎉 Chat funcionando diretamente!")
    else:
        print("\n❌ Chat não funcionou.")
