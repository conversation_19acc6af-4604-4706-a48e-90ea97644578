#!/usr/bin/env python3
"""
Sistema TURBO: LLM Local + Gemini
O LLM local dá ARRANQUE e velocidade!
"""

import asyncio
import json
import os
import time
import requests
from pathlib import Path
from typing import List, Dict, Any, Optional

# Configuração
GOOGLE_API_KEY = "AIzaSyCq1rBFLFqX04jQ2ejd5Tkbane-lTvIsVU"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

class TurboLocal:
    """LLM Local para ARRANQUE rápido"""
    
    def __init__(self, config: Dict):
        self.config = config.get("llm_local", {})
        self.base_url = self.config.get("base_url", "http://localhost:11434")
        self.model = self.config.get("model", "tinyllama")
        self.ativo = False
        self.verificar_status()
    
    def verificar_status(self):
        """Verifica se Ollama está rodando"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=3)
            if response.status_code == 200:
                self.ativo = True
                print(f"⚡ Turbo Local ATIVO: {self.model}")
            else:
                print("⚠️ Ollama não está rodando")
        except:
            print("⚠️ Turbo Local não disponível")
    
    async def processar_rapido(self, tarefa: str, texto: str) -> Dict[str, Any]:
        """Processamento RÁPIDO para dar arranque"""
        if not self.ativo:
            return {"erro": "Turbo Local inativo"}
        
        inicio = time.time()
        
        try:
            # Prompts otimizados para velocidade
            prompts = {
                "extrair_topicos": f"Liste 5 tópicos principais em: {texto[:500]}",
                "categorizar": f"Categorize este texto jurídico: {texto[:300]}",
                "resumir": f"Resumo em 2 frases: {texto[:400]}",
                "palavras_chave": f"5 palavras-chave de: {texto[:200]}"
            }
            
            prompt = prompts.get(tarefa, f"Analise: {texto[:300]}")
            
            # Requisição rápida ao Ollama
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Mais determinístico = mais rápido
                    "num_predict": 100   # Resposta curta = mais rápido
                }
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                resultado = response.json()
                tempo = time.time() - inicio
                
                return {
                    "sucesso": True,
                    "resultado": resultado.get("response", ""),
                    "tempo": round(tempo, 2),
                    "tarefa": tarefa
                }
            else:
                return {"erro": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {"erro": str(e)}

class GeminiEspecialista:
    """Gemini para análises profundas"""
    
    def __init__(self):
        self.llm = None
        self.ativo = False
        self.carregar()
    
    def carregar(self):
        """Carrega Gemini"""
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            self.llm = ChatGoogleGenerativeAI(
                model="gemini-1.5-pro",
                temperature=0.3,
                google_api_key=GOOGLE_API_KEY
            )
            
            self.ativo = True
            print("🧠 Gemini Especialista ATIVO")
            
        except Exception as e:
            print(f"❌ Gemini: {e}")
    
    async def analisar_profundo(self, pergunta: str, contexto_turbo: Dict, documentos: List[Dict]) -> str:
        """Análise profunda usando contexto do Turbo Local"""
        if not self.ativo:
            return "❌ Gemini não disponível"
        
        # Monta contexto enriquecido pelo Turbo Local
        contexto_rapido = ""
        if contexto_turbo.get("sucesso"):
            contexto_rapido = f"""
ANÁLISE RÁPIDA (Turbo Local em {contexto_turbo.get('tempo')}s):
{contexto_turbo.get('resultado', '')}

"""
        
        # Adiciona documentos
        docs_texto = "\n".join([doc.get('content', '')[:300] for doc in documentos[:3]])
        
        prompt = f"""
{contexto_rapido}
DOCUMENTOS RELEVANTES:
{docs_texto}

PERGUNTA: {pergunta}

Como especialista jurídico, forneça análise completa considerando:
1. A análise rápida já feita
2. Os documentos fornecidos  
3. Seu conhecimento especializado

Responda em português de forma clara e estruturada.
"""
        
        try:
            from langchain.schema import HumanMessage
            
            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            return response.content
            
        except Exception as e:
            return f"❌ Erro Gemini: {e}"

class SistemaTurbo:
    """Sistema TURBO: Velocidade + Inteligência"""
    
    def __init__(self):
        print("🚀 SISTEMA TURBO INICIALIZANDO...")
        print("=" * 40)
        
        # Carrega configuração
        self.config = self.carregar_config()
        
        # Inicializa componentes
        self.turbo = TurboLocal(self.config)
        self.gemini = GeminiEspecialista()
        
        # Status
        print(f"⚡ Turbo Local: {'✅ ATIVO' if self.turbo.ativo else '❌ INATIVO'}")
        print(f"🧠 Gemini: {'✅ ATIVO' if self.gemini.ativo else '❌ INATIVO'}")
        
        if self.turbo.ativo or self.gemini.ativo:
            print("🔥 SISTEMA TURBO PRONTO!")
        else:
            print("❌ Nenhum LLM disponível")
    
    def carregar_config(self):
        """Carrega configuração turbo"""
        config_file = "config_turbo.json"
        
        if Path(config_file).exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # Config padrão
            return {
                "llm_local": {
                    "enabled": True,
                    "model": "tinyllama",
                    "base_url": "http://localhost:11434"
                },
                "modo_turbo": {
                    "enabled": True,
                    "usar_local_para": ["extrair_topicos", "categorizar", "resumir"]
                }
            }
    
    async def processar_turbo(self, pergunta: str, documentos: List[Dict] = None) -> str:
        """Processamento TURBO: Local + Gemini"""
        
        print(f"🚀 MODO TURBO: {pergunta}")
        
        if not documentos:
            documentos = []
        
        inicio_total = time.time()
        
        # FASE 1: ARRANQUE com Turbo Local (RÁPIDO!)
        contexto_turbo = {}
        if self.turbo.ativo and documentos:
            print("⚡ FASE 1: Turbo Local dando arranque...")
            
            # Combina documentos para análise rápida
            texto_docs = "\n".join([doc.get('content', '') for doc in documentos[:2]])
            
            # Análise rápida
            contexto_turbo = await self.turbo.processar_rapido("extrair_topicos", texto_docs)
            
            if contexto_turbo.get("sucesso"):
                print(f"✅ Arranque em {contexto_turbo['tempo']}s!")
            else:
                print("⚠️ Arranque com problemas")
        
        # FASE 2: Análise profunda com Gemini
        if self.gemini.ativo:
            print("🧠 FASE 2: Gemini analisando profundamente...")
            
            resposta = await self.gemini.analisar_profundo(pergunta, contexto_turbo, documentos)
            
            tempo_total = time.time() - inicio_total
            
            return f"""🚀 [TURBO] Processado em {tempo_total:.1f}s

{resposta}

---
⚡ Arranque Local: {contexto_turbo.get('tempo', 0)}s
🧠 Análise Gemini: {tempo_total - contexto_turbo.get('tempo', 0):.1f}s
"""
        
        # Fallback: só Turbo Local
        elif self.turbo.ativo:
            print("⚡ Usando apenas Turbo Local...")
            
            texto_pergunta = f"{pergunta} {' '.join([doc.get('content', '')[:200] for doc in documentos])}"
            resultado = await self.turbo.processar_rapido("resumir", texto_pergunta)
            
            if resultado.get("sucesso"):
                return f"⚡ [TURBO LOCAL] {resultado['resultado']}"
            else:
                return "❌ Erro no processamento turbo"
        
        else:
            return "❌ Nenhum LLM disponível"

async def demo_turbo():
    """Demo do sistema turbo"""
    print("🎯 DEMO SISTEMA TURBO")
    print("=" * 25)
    
    # Inicializa
    sistema = SistemaTurbo()
    
    # Documentos de teste
    docs_teste = [
        {
            'content': 'O direito penal brasileiro estabelece que homicídio é crime contra a vida, previsto no artigo 121 do Código Penal. A pena varia de 6 a 20 anos.',
            'metadata': {'source': 'penal.md'}
        },
        {
            'content': 'O processo penal deve observar o princípio da presunção de inocência, garantindo que ninguém seja considerado culpado antes do trânsito em julgado.',
            'metadata': {'source': 'processo.md'}
        }
    ]
    
    # Perguntas teste
    perguntas = [
        "O que é homicídio no direito penal?",
        "Explique presunção de inocência",
        "Quais são os principais crimes contra a vida?"
    ]
    
    for pergunta in perguntas:
        print(f"\n{'='*60}")
        resposta = await sistema.processar_turbo(pergunta, docs_teste)
        print(resposta)

async def main():
    """Menu principal"""
    print("🚀 SISTEMA TURBO OBSIDIAN")
    print("=" * 30)
    print("LLM Local para ARRANQUE + Gemini para INTELIGÊNCIA")
    print()
    
    print("Escolha:")
    print("1. 🧪 Demo Turbo")
    print("2. ⚙️ Instalar LLM Local")
    print("3. 🌐 Interface Web")
    
    try:
        choice = input("\nOpção: ").strip()
        
        if choice == "1":
            await demo_turbo()
        elif choice == "2":
            print("🔧 Executando instalador...")
            os.system("python instalar_ollama_rapido.py")
        elif choice == "3":
            print("🌐 Abrindo interface...")
            os.system("streamlit run web_interface.py")
        else:
            print("❌ Opção inválida")
            
    except KeyboardInterrupt:
        print("\n👋 Sistema encerrado")

if __name__ == "__main__":
    asyncio.run(main())
