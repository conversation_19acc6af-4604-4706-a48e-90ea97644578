// Gerenciador do Chat
class ChatManager {
    constructor() {
        this.messagesContainer = document.getElementById('chat-messages');
        this.messageInput = document.getElementById('message-input');
        this.sendButton = document.getElementById('send-btn');
        this.isProcessing = false;

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Enviar mensagem com Enter
        this.messageInput?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Botão enviar
        this.sendButton?.addEventListener('click', () => {
            this.sendMessage();
        });

        // Auto-resize do textarea
        this.messageInput?.addEventListener('input', () => {
            this.autoResizeTextarea();
        });
    }

    autoResizeTextarea() {
        const textarea = this.messageInput;
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();

        if (!message || this.isProcessing) {
            return;
        }

        this.isProcessing = true;
        this.updateSendButton(true);

        try {
            // Adiciona mensagem do usuário
            this.addMessage('user', message);

            // Limpa input
            this.messageInput.value = '';
            this.autoResizeTextarea();

            // Mostra indicador de digitação
            const typingId = this.showTypingIndicator();

            // Envia para API
            const response = await Utils.apiCall('/api/chat', {
                method: 'POST',
                body: JSON.stringify({
                    message: message,
                    session_id: currentSessionId
                })
            });

            // Remove indicador de digitação
            this.removeTypingIndicator(typingId);

            // Adiciona resposta do assistente
            this.addMessage('assistant', response.response, response.sources, false, response.command_type);

            // Atualiza session_id se necessário
            if (response.session_id) {
                currentSessionId = response.session_id;
            }

            // Se foi um comando de edição, atualiza a lista de notas
            if (response.command_type && response.command_type !== 'chat') {
                setTimeout(() => {
                    if (window.NotesManager) {
                        NotesManager.loadNotes();
                    }
                }, 1000);
            }

        } catch (error) {
            this.removeTypingIndicator();
            this.addMessage('assistant', 'Desculpe, ocorreu um erro ao processar sua mensagem. Tente novamente.', [], true);
            Utils.showToast('Erro ao enviar mensagem: ' + error.message, 'error');
        } finally {
            this.isProcessing = false;
            this.updateSendButton(false);
        }
    }

    addMessage(role, content, sources = [], isError = false, commandType = 'chat') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}-message`;

        const avatar = role === 'user' ?
            '<i class="fas fa-user"></i>' :
            this.getAssistantAvatar(commandType);

        let sourcesHtml = '';
        if (sources && sources.length > 0) {
            sourcesHtml = `
                <div class="message-sources">
                    <strong>Fontes:</strong>
                    ${sources.map(source => `
                        <div class="source-item">
                            📄 ${source.title} (Similaridade: ${(source.similarity_score * 100).toFixed(1)}%)
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // Adiciona indicador de comando se não for chat normal
        let commandIndicator = '';
        if (role === 'assistant' && commandType !== 'chat') {
            commandIndicator = this.getCommandIndicator(commandType);
        }

        messageDiv.innerHTML = `
            <div class="message-avatar">
                ${avatar}
            </div>
            <div class="message-content">
                ${commandIndicator}
                <div class="message-text ${isError ? 'error' : ''}">
                    ${this.formatMessageContent(content)}
                </div>
                ${sourcesHtml}
            </div>
        `;

        this.messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    getAssistantAvatar(commandType) {
        """Retorna avatar apropriado baseado no tipo de comando"""
        switch(commandType) {
            case 'modify_note':
                return '<i class="fas fa-edit" style="color: #ffc107;"></i>';
            case 'create_note':
                return '<i class="fas fa-plus-circle" style="color: #28a745;"></i>';
            case 'add_to_note':
                return '<i class="fas fa-plus" style="color: #17a2b8;"></i>';
            default:
                return '<i class="fas fa-robot"></i>';
        }
    }

    getCommandIndicator(commandType) {
        """Retorna indicador visual para o tipo de comando"""
        const indicators = {
            'modify_note': '<div class="command-indicator modify">✏️ Nota Modificada</div>',
            'create_note': '<div class="command-indicator create">✨ Nova Nota Criada</div>',
            'add_to_note': '<div class="command-indicator add">➕ Conteúdo Adicionado</div>'
        };

        return indicators[commandType] || '';
    }

    formatMessageContent(content) {
        // Converte quebras de linha em <br>
        content = content.replace(/\n/g, '<br>');

        // Converte **texto** em <strong>texto</strong>
        content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Converte *texto* em <em>texto</em>
        content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // Converte listas
        content = content.replace(/^- (.*$)/gim, '<li>$1</li>');
        content = content.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');

        return content;
    }

    showTypingIndicator() {
        const typingId = 'typing-' + Date.now();
        const typingDiv = document.createElement('div');
        typingDiv.id = typingId;
        typingDiv.className = 'message assistant-message typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="message-content">
                <div class="message-text">
                    <div class="typing-animation">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    Pensando...
                </div>
            </div>
        `;

        this.messagesContainer.appendChild(typingDiv);
        this.scrollToBottom();

        return typingId;
    }

    removeTypingIndicator(typingId = null) {
        if (typingId) {
            const element = document.getElementById(typingId);
            if (element) {
                element.remove();
            }
        } else {
            // Remove todos os indicadores de digitação
            const indicators = this.messagesContainer.querySelectorAll('.typing-indicator');
            indicators.forEach(indicator => indicator.remove());
        }
    }

    updateSendButton(isProcessing) {
        if (this.sendButton) {
            if (isProcessing) {
                this.sendButton.disabled = true;
                this.sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            } else {
                this.sendButton.disabled = false;
                this.sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
            }
        }
    }

    scrollToBottom() {
        setTimeout(() => {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }, 100);
    }

    clearChat() {
        // Remove todas as mensagens exceto a de boas-vindas
        const messages = this.messagesContainer.querySelectorAll('.message');
        messages.forEach((message, index) => {
            if (index > 0) { // Mantém a primeira mensagem (boas-vindas)
                message.remove();
            }
        });

        // Limpa input
        if (this.messageInput) {
            this.messageInput.value = '';
            this.autoResizeTextarea();
        }

        Utils.showToast('Chat limpo com sucesso', 'success');
    }

    async loadChatHistory() {
        if (!currentSessionId) return;

        try {
            const response = await Utils.apiCall(`/api/chat/history/${currentSessionId}`);
            const history = response.history;

            // Limpa mensagens atuais (exceto boas-vindas)
            const messages = this.messagesContainer.querySelectorAll('.message');
            messages.forEach((message, index) => {
                if (index > 0) {
                    message.remove();
                }
            });

            // Adiciona mensagens do histórico
            history.forEach(msg => {
                if (msg.role === 'user') {
                    this.addMessage('user', msg.content);
                } else {
                    const sources = msg.metadata?.context_sources || [];
                    this.addMessage('assistant', msg.content, sources);
                }
            });

        } catch (error) {
            console.error('Erro ao carregar histórico:', error);
        }
    }
}

// Adiciona estilos CSS para animação de digitação
const typingStyles = `
    .typing-animation {
        display: inline-flex;
        align-items: center;
        margin-right: 8px;
    }

    .typing-animation span {
        height: 8px;
        width: 8px;
        background: #667eea;
        border-radius: 50%;
        display: inline-block;
        margin-right: 3px;
        animation: typing 1.4s infinite ease-in-out;
    }

    .typing-animation span:nth-child(1) {
        animation-delay: -0.32s;
    }

    .typing-animation span:nth-child(2) {
        animation-delay: -0.16s;
    }

    @keyframes typing {
        0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
        }
        40% {
            transform: scale(1);
            opacity: 1;
        }
    }

    .message-text.error {
        background: #f8d7da !important;
        color: #721c24 !important;
        border-left: 4px solid #dc3545;
    }

    .command-indicator {
        font-size: 12px;
        padding: 4px 8px;
        border-radius: 12px;
        margin-bottom: 8px;
        font-weight: 500;
        display: inline-block;
    }

    .command-indicator.modify {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .command-indicator.create {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .command-indicator.add {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
    }
`;

// Adiciona estilos ao documento
const styleSheet = document.createElement('style');
styleSheet.textContent = typingStyles;
document.head.appendChild(styleSheet);

// Inicializa o gerenciador de chat quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    window.ChatManager = new ChatManager();

    // Carrega histórico se houver session_id
    if (currentSessionId) {
        window.ChatManager.loadChatHistory();
    }
});

// Função global para limpar chat (usada em app.js)
window.clearChat = function() {
    if (window.ChatManager) {
        window.ChatManager.clearChat();
    }
};
