// Configuração global da aplicação
const API_BASE_URL = window.location.origin;
let currentSessionId = null;

// Utilitários
class Utils {
    static async apiCall(endpoint, options = {}) {
        const url = `${API_BASE_URL}${endpoint}`;
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const finalOptions = { ...defaultOptions, ...options };

        try {
            const response = await fetch(url, finalOptions);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    static showToast(message, type = 'success') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        container.appendChild(toast);

        // Remove toast após 5 segundos
        setTimeout(() => {
            toast.remove();
        }, 5000);
    }

    static showLoading(show = true) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.add('show');
        } else {
            overlay.classList.remove('show');
        }
    }

    static formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('pt-BR');
    }

    static generateSessionId() {
        return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }
}

// Gerenciador de Estado
class StateManager {
    constructor() {
        this.state = {
            isIndexing: false,
            stats: {
                totalDocuments: 0,
                totalChunks: 0
            },
            config: {},
            searchResults: []
        };
        this.listeners = [];
    }

    setState(newState) {
        this.state = { ...this.state, ...newState };
        this.notifyListeners();
    }

    getState() {
        return this.state;
    }

    subscribe(listener) {
        this.listeners.push(listener);
        return () => {
            this.listeners = this.listeners.filter(l => l !== listener);
        };
    }

    notifyListeners() {
        this.listeners.forEach(listener => listener(this.state));
    }
}

// Instância global do gerenciador de estado
const stateManager = new StateManager();

// Gerenciador de Configurações
class ConfigManager {
    static async loadConfig() {
        try {
            const config = await Utils.apiCall('/api/config');
            stateManager.setState({ config });
            return config;
        } catch (error) {
            Utils.showToast('Erro ao carregar configurações', 'error');
            return {};
        }
    }

    static async checkApiStatus() {
        try {
            const response = await Utils.apiCall('/health');
            return response.status === 'healthy';
        } catch (error) {
            return false;
        }
    }

    static async updateApiStatus() {
        const statusDot = document.querySelector('.status-dot');
        const statusText = document.querySelector('.status-text');

        statusDot.className = 'status-dot loading';
        statusText.textContent = 'Verificando...';

        const isOnline = await this.checkApiStatus();

        if (isOnline) {
            statusDot.className = 'status-dot online';
            statusText.textContent = 'API Online';
        } else {
            statusDot.className = 'status-dot';
            statusText.textContent = 'API Offline';
        }
    }
}

// Gerenciador de Estatísticas
class StatsManager {
    static async loadStats() {
        try {
            const stats = await Utils.apiCall('/api/index/stats');
            stateManager.setState({
                stats: {
                    totalDocuments: stats.total_documents || 0,
                    totalChunks: stats.total_chunks || 0
                }
            });
            this.updateStatsDisplay(stats);
        } catch (error) {
            console.error('Erro ao carregar estatísticas:', error);
        }
    }

    static updateStatsDisplay(stats) {
        const docsCount = document.getElementById('docs-count');
        const chunksCount = document.getElementById('chunks-count');

        if (docsCount) docsCount.textContent = stats.total_documents || 0;
        if (chunksCount) chunksCount.textContent = stats.total_chunks || 0;
    }
}

// Gerenciador de Indexação
class IndexManager {
    static async indexVault(forceReindex = false) {
        try {
            Utils.showLoading(true);
            Utils.showToast('Iniciando indexação do vault...', 'info');

            const result = await Utils.apiCall('/api/index', {
                method: 'POST',
                body: JSON.stringify({ force_reindex: forceReindex })
            });

            Utils.showToast(`Indexação concluída! ${result.stats.indexed_files} arquivos indexados.`, 'success');

            // Atualiza estatísticas
            await StatsManager.loadStats();

        } catch (error) {
            Utils.showToast('Erro na indexação: ' + error.message, 'error');
        } finally {
            Utils.showLoading(false);
        }
    }
}

// Gerenciador de Upload
class UploadManager {
    static async uploadFile(file) {
        try {
            Utils.showLoading(true);

            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch(`${API_BASE_URL}/api/upload`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Upload failed: ${response.statusText}`);
            }

            const result = await response.json();
            Utils.showToast(result.message, 'success');

            // Atualiza estatísticas
            await StatsManager.loadStats();

        } catch (error) {
            Utils.showToast('Erro no upload: ' + error.message, 'error');
        } finally {
            Utils.showLoading(false);
        }
    }
}

// Gerenciador de Notas
class NotesManager {
    static async loadNotes() {
        // Carrega lista de notas
        try {
            const notes = await Utils.apiCall('/api/notes');
            this.displayNotes(notes.notes);
        } catch (error) {
            console.error('Erro ao carregar notas:', error);
        }
    }

    static displayNotes(notes) {
        // Exibe lista de notas na sidebar
        const container = document.getElementById('notes-list');

        if (!notes || notes.length === 0) {
            container.innerHTML = '<p class="no-notes">Nenhuma nota encontrada</p>';
            return;
        }

        const html = notes.map(note => `
            <div class="note-item" onclick="NotesManager.selectNote('${note.filename}')">
                <div class="note-item-title">${note.title}</div>
                <div class="note-item-preview">${note.preview}</div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    static selectNote(filename) {
        // Seleciona uma nota e insere comando no input
        const input = document.getElementById('message-input');
        const noteName = filename.replace('.md', '');
        input.value = `Me fale sobre a nota "${noteName}"`;
        input.focus();
    }

    static async refreshNotes() {
        // Atualiza lista de notas
        Utils.showLoading(true);
        await this.loadNotes();
        Utils.showLoading(false);
        Utils.showToast('Lista de notas atualizada', 'success');
    }
}

// Gerenciador de Comandos de Edição
class EditCommandsManager {
    static async getExamples() {
        // Obtém exemplos de comandos de edição
        try {
            const response = await Utils.apiCall('/api/commands/examples');
            return response;
        } catch (error) {
            console.error('Erro ao carregar exemplos:', error);
            return null;
        }
    }

    static showEditHelp() {
        // Mostra ajuda sobre comandos de edição
        const helpText = `
        🔧 **Comandos de Edição Disponíveis:**

        **Criar Nova Nota:**
        • "Crie uma nova nota sobre [tópico]"
        • "Faça uma nota chamada '[título]'"

        **Modificar Nota Existente:**
        • "Modifique a nota '[título]' adicionando [conteúdo]"
        • "Atualize a nota sobre [tópico]"

        **Adicionar Conteúdo:**
        • "Adicione [conteúdo] à nota '[título]'"
        • "Inclua uma seção de [tópico] na nota [título]"

        **Exemplos Práticos:**
        • "Crie uma nota sobre Inteligência Artificial"
        • "Modifique a nota 'Direito Constitucional' adicionando exemplos"
        • "Adicione uma conclusão à nota sobre neurociência"
        `;

        Utils.showToast(helpText, 'info');
    }
}

// Gerenciador de Busca Rápida
class QuickSearchManager {
    static async search(query) {
        if (!query.trim()) {
            this.clearResults();
            return;
        }

        try {
            const results = await Utils.apiCall('/api/search', {
                method: 'POST',
                body: JSON.stringify({
                    query: query,
                    search_type: 'hybrid',
                    top_k: 5
                })
            });

            this.displayResults(results.results);

        } catch (error) {
            console.error('Erro na busca:', error);
            this.clearResults();
        }
    }

    static displayResults(results) {
        const container = document.getElementById('search-results');

        if (!results || results.length === 0) {
            container.innerHTML = '<p class="no-results">Nenhum resultado encontrado</p>';
            return;
        }

        const html = results.map(result => `
            <div class="search-result-item" onclick="QuickSearchManager.insertSearchResult('${result.document_title}')">
                <div class="search-result-title">${result.document_title}</div>
                <div class="search-result-preview">${this.truncateText(result.chunk_content, 100)}</div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    static clearResults() {
        const container = document.getElementById('search-results');
        container.innerHTML = '';
    }

    static insertSearchResult(title) {
        const input = document.getElementById('message-input');
        input.value = `Me fale sobre: ${title}`;
        input.focus();
        this.clearResults();
    }

    static truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substr(0, maxLength) + '...';
    }
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Gera session ID
    currentSessionId = Utils.generateSessionId();

    // Carrega configurações e estatísticas iniciais
    ConfigManager.loadConfig();
    ConfigManager.updateApiStatus();
    StatsManager.loadStats();
    NotesManager.loadNotes();

    // Botão Nova Conversa
    document.getElementById('new-chat-btn')?.addEventListener('click', function() {
        currentSessionId = Utils.generateSessionId();
        // Limpa o chat (implementado no chat.js)
        if (window.ChatManager) {
            ChatManager.clearChat();
        }
        Utils.showToast('Nova conversa iniciada', 'success');
    });

    // Botão Indexar Vault
    document.getElementById('index-vault-btn')?.addEventListener('click', function() {
        const forceReindex = confirm('Deseja forçar a reindexação de todos os documentos?');
        IndexManager.indexVault(forceReindex);
    });

    // Upload de arquivo
    document.getElementById('upload-file-btn')?.addEventListener('click', function() {
        document.getElementById('file-input').click();
    });

    document.getElementById('file-input')?.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            if (!file.name.endsWith('.md')) {
                Utils.showToast('Apenas arquivos .md são aceitos', 'error');
                return;
            }
            UploadManager.uploadFile(file);
        }
    });

    // Busca rápida
    let searchTimeout;
    document.getElementById('quick-search')?.addEventListener('input', function(e) {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            QuickSearchManager.search(e.target.value);
        }, 300);
    });

    document.getElementById('search-btn')?.addEventListener('click', function() {
        const query = document.getElementById('quick-search').value;
        QuickSearchManager.search(query);
    });

    // Limpar chat
    document.getElementById('clear-chat-btn')?.addEventListener('click', function() {
        if (confirm('Deseja limpar o histórico desta conversa?')) {
            if (window.ChatManager) {
                ChatManager.clearChat();
            }
        }
    });

    // Modal de configurações
    document.getElementById('settings-btn')?.addEventListener('click', function() {
        openSettingsModal();
    });

    // Atualizar lista de notas
    document.getElementById('refresh-notes-btn')?.addEventListener('click', function() {
        NotesManager.refreshNotes();
    });

    // Sugestões de input
    document.querySelectorAll('.suggestion-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const text = this.getAttribute('data-text');
            const input = document.getElementById('message-input');
            input.value = text;
            input.focus();
        });
    });
});

// Funções do Modal
function openSettingsModal() {
    const modal = document.getElementById('settings-modal');
    const vaultPath = document.getElementById('vault-path');

    // Carrega configurações atuais
    const config = stateManager.getState().config;
    if (vaultPath && config.vault_path) {
        vaultPath.value = config.vault_path;
    }

    modal.classList.add('show');
    ConfigManager.updateApiStatus();
}

function closeModal() {
    const modal = document.getElementById('settings-modal');
    modal.classList.remove('show');
}

// Fecha modal ao clicar fora
document.addEventListener('click', function(e) {
    const modal = document.getElementById('settings-modal');
    if (e.target === modal) {
        closeModal();
    }
});

// Fecha modal com ESC
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
});

// Atualiza estatísticas periodicamente
setInterval(() => {
    StatsManager.loadStats();
    ConfigManager.updateApiStatus();
}, 30000); // A cada 30 segundos
