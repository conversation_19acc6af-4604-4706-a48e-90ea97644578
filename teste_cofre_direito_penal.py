#!/usr/bin/env python3
"""
Teste específico para o cofre de Direito Penal
"""

import asyncio
import os
from pathlib import Path
from obsidian_multi_agent_system import ObsidianMultiAgentSystem

async def testar_direito_penal():
    """Testa especificamente o cofre de Direito Penal"""
    print("🧪 TESTE DO COFRE DIREITO PENAL")
    print("=" * 40)
    
    # Caminho do cofre
    caminho_cofre = r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Direito Penal"
    
    print(f"📁 Testando caminho: {caminho_cofre}")
    
    # Verifica se existe
    path = Path(caminho_cofre)
    if not path.exists():
        print(f"❌ Caminho não existe: {caminho_cofre}")
        return False
    
    print("✅ Caminho existe!")
    
    # Lista arquivos .md
    try:
        md_files = list(path.rglob("*.md"))
        print(f"📄 Encontrados {len(md_files)} arquivos .md")
        
        if len(md_files) > 0:
            print("📋 Primeiros 5 arquivos:")
            for i, file in enumerate(md_files[:5]):
                print(f"   {i+1}. {file.name}")
        
        if len(md_files) == 0:
            print("⚠️ Nenhum arquivo .md encontrado")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao listar arquivos: {e}")
        return False
    
    # Testa carregamento no sistema
    print("\n🤖 Testando carregamento no sistema...")
    
    try:
        system = ObsidianMultiAgentSystem()
        
        sucesso = system.add_vault(
            "Direito Penal", 
            caminho_cofre, 
            "Notas sobre direito penal e processo penal"
        )
        
        if sucesso:
            print("✅ Cofre carregado com sucesso!")
            
            # Testa indexação
            print("🔄 Testando indexação...")
            await system.index_all_vaults()
            print("✅ Indexação concluída!")
            
            # Estatísticas
            stats = system.get_vault_stats()
            print(f"\n📊 Estatísticas:")
            print(f"   • Cofres: {stats['total_vaults']}")
            print(f"   • Arquivos: {stats['total_files']}")
            
            # Teste de chat
            print("\n💬 Testando chat...")
            resposta = await system.chat("Quais são os principais temas de direito penal?")
            print(f"🤖 Resposta: {resposta[:200]}...")
            
            return True
            
        else:
            print("❌ Falha ao carregar cofre")
            return False
            
    except Exception as e:
        print(f"❌ Erro no sistema: {e}")
        return False

async def main():
    """Função principal"""
    try:
        sucesso = await testar_direito_penal()
        
        if sucesso:
            print("\n🎉 TESTE CONCLUÍDO COM SUCESSO!")
            print("💡 O cofre de Direito Penal está funcionando perfeitamente!")
        else:
            print("\n❌ TESTE FALHOU")
            print("💡 Verifique os caminhos e tente novamente")
            
    except KeyboardInterrupt:
        print("\n👋 Teste interrompido")
    except Exception as e:
        print(f"\n❌ Erro geral: {e}")

if __name__ == "__main__":
    asyncio.run(main())
