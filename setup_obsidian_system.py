#!/usr/bin/env python3
"""
Script de Configuração Automática
Sistema Multi-Agente Obsidian
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_header():
    """Imprime cabeçalho do setup"""
    print("=" * 60)
    print("🧠 SISTEMA MULTI-AGENTE OBSIDIAN")
    print("🚀 Configuração Automática")
    print("=" * 60)
    print()

def check_python_version():
    """Verifica versão do Python"""
    print("🔍 Verificando versão do Python...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ é necessário")
        print(f"   Versão atual: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detectado")
    return True

def install_requirements():
    """Instala dependências"""
    print("\n📦 Instalando dependências...")
    
    try:
        # Atualiza pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # Instala requirements
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements_obsidian.txt"], 
                      check=True, capture_output=True)
        
        print("✅ Dependências instaladas com sucesso!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao instalar dependências: {e}")
        print("💡 Tente executar manualmente:")
        print("   pip install -r requirements_obsidian.txt")
        return False

def setup_environment():
    """Configura variáveis de ambiente"""
    print("\n🔧 Configurando ambiente...")
    
    # Verifica se .env existe
    env_file = Path(".env")
    
    if not env_file.exists():
        print("📝 Criando arquivo .env...")
        
        env_content = f"""# Configuração do Sistema Multi-Agente Obsidian
GOOGLE_API_KEY=AIzaSyDASqL2UXOSEuXX0UTTOXzYciyoUwuk1mc

# Configurações opcionais
OBSIDIAN_VAULT_PATH=C:/Users/<USER>/Documents/Obsidian
LOG_LEVEL=INFO
MAX_TOKENS=4000
TEMPERATURE=0.3
"""
        
        with open(env_file, "w", encoding="utf-8") as f:
            f.write(env_content)
        
        print("✅ Arquivo .env criado")
    else:
        print("✅ Arquivo .env já existe")
    
    return True

def create_example_config():
    """Cria configuração de exemplo"""
    print("\n📋 Criando configuração de exemplo...")
    
    config = {
        "vaults": [
            {
                "name": "Direito",
                "path": "C:/Users/<USER>/Documents/Obsidian/Direito",
                "description": "Notas sobre direito constitucional, civil e penal"
            },
            {
                "name": "Neurociência",
                "path": "C:/Users/<USER>/Documents/Obsidian/Neurociencia",
                "description": "Estudos sobre neurociência, psicologia e comportamento"
            },
            {
                "name": "Marketing",
                "path": "C:/Users/<USER>/Documents/Obsidian/Marketing",
                "description": "Estratégias de marketing digital e tradicional"
            },
            {
                "name": "Filosofia",
                "path": "C:/Users/<USER>/Documents/Obsidian/Filosofia",
                "description": "Filosofia, ética e pensamento crítico"
            }
        ],
        "settings": {
            "max_tokens": 4000,
            "temperature": 0.3,
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "similarity_threshold": 0.7
        }
    }
    
    with open("config_example.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ Arquivo config_example.json criado")
    print("💡 Edite este arquivo com os caminhos dos seus cofres")

def create_run_scripts():
    """Cria scripts de execução"""
    print("\n📜 Criando scripts de execução...")
    
    # Script para Windows
    run_bat = """@echo off
echo 🧠 Iniciando Sistema Multi-Agente Obsidian
echo.
python obsidian_multi_agent_system.py
pause
"""
    
    with open("run_system.bat", "w", encoding="utf-8") as f:
        f.write(run_bat)
    
    # Script para interface web
    web_bat = """@echo off
echo 🌐 Iniciando Interface Web
echo.
echo Acesse: http://localhost:8501
echo.
streamlit run web_interface.py
pause
"""
    
    with open("run_web.bat", "w", encoding="utf-8") as f:
        f.write(web_bat)
    
    # Script para Linux/Mac
    run_sh = """#!/bin/bash
echo "🧠 Iniciando Sistema Multi-Agente Obsidian"
echo
python3 obsidian_multi_agent_system.py
"""
    
    with open("run_system.sh", "w", encoding="utf-8") as f:
        f.write(run_sh)
    
    # Torna executável no Linux/Mac
    try:
        os.chmod("run_system.sh", 0o755)
    except:
        pass
    
    print("✅ Scripts de execução criados:")
    print("   • run_system.bat (Windows)")
    print("   • run_web.bat (Interface Web)")
    print("   • run_system.sh (Linux/Mac)")

def test_installation():
    """Testa a instalação"""
    print("\n🧪 Testando instalação...")
    
    try:
        # Testa imports principais
        import langchain
        import streamlit
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        print("✅ LangChain importado com sucesso")
        print("✅ Streamlit importado com sucesso")
        print("✅ Google Generative AI importado com sucesso")
        
        # Testa sistema
        print("🔄 Testando sistema...")
        from obsidian_multi_agent_system import ObsidianMultiAgentSystem
        
        system = ObsidianMultiAgentSystem()
        print("✅ Sistema Multi-Agente inicializado com sucesso")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

def print_instructions():
    """Imprime instruções finais"""
    print("\n" + "=" * 60)
    print("🎉 CONFIGURAÇÃO CONCLUÍDA!")
    print("=" * 60)
    print()
    print("📋 PRÓXIMOS PASSOS:")
    print()
    print("1. 📝 Edite config_example.json com os caminhos dos seus cofres")
    print("2. 🌐 Execute a interface web:")
    print("   • Windows: run_web.bat")
    print("   • Ou: streamlit run web_interface.py")
    print()
    print("3. 💬 Ou use diretamente:")
    print("   • Windows: run_system.bat")
    print("   • Linux/Mac: ./run_system.sh")
    print("   • Ou: python obsidian_multi_agent_system.py")
    print()
    print("🔧 CONFIGURAÇÃO:")
    print("   • API Key Gemini: Já configurada")
    print("   • Cofres: Edite config_example.json")
    print("   • Logs: Verifique console para debug")
    print()
    print("💡 DICAS:")
    print("   • Use a interface web para facilidade")
    print("   • Adicione seus cofres na sidebar")
    print("   • Indexe antes de conversar")
    print("   • Experimente os comandos de exemplo")
    print()
    print("🆘 SUPORTE:")
    print("   • Verifique se os caminhos dos cofres estão corretos")
    print("   • Certifique-se que os arquivos .md existem")
    print("   • API Key Gemini deve estar válida")
    print()
    print("🚀 PRONTO PARA USO!")

def main():
    """Função principal do setup"""
    print_header()
    
    # Verificações
    if not check_python_version():
        return False
    
    # Instalação
    if not install_requirements():
        return False
    
    # Configuração
    setup_environment()
    create_example_config()
    create_run_scripts()
    
    # Teste
    if not test_installation():
        print("\n⚠️ Instalação com problemas, mas arquivos criados")
        print("💡 Tente executar manualmente para debug")
    
    # Instruções finais
    print_instructions()
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelado pelo usuário")
    except Exception as e:
        print(f"\n\n❌ Erro durante setup: {e}")
        print("💡 Tente executar novamente ou instale manualmente")
