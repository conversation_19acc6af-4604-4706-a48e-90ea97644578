#!/usr/bin/env python3
"""
Script para carregar automaticamente os cofres do Alexandre
Configurado especificamente para os caminhos do iCloud
"""

import asyncio
import json
import os
from pathlib import Path
from obsidian_multi_agent_system import ObsidianMultiAgentSystem

def verificar_caminho(caminho):
    """Verifica se um caminho existe e tem arquivos .md"""
    try:
        path = Path(caminho)
        if not path.exists():
            return False, f"Caminho não existe: {caminho}"
        
        # Conta arquivos .md
        md_files = list(path.rglob("*.md"))
        if len(md_files) == 0:
            return False, f"Nenhum arquivo .md encontrado em: {caminho}"
        
        return True, f"✅ {len(md_files)} arquivos .md encontrados"
    
    except Exception as e:
        return False, f"Erro ao verificar: {e}"

async def carregar_cofres_alexandre():
    """Carrega todos os cofres do Alexandre automaticamente"""
    print("🧠 CARREGANDO COFRES DO ALEXANDRE")
    print("=" * 50)
    
    # Carrega configuração
    config_file = "config_cofres_alexandre.json"
    if not Path(config_file).exists():
        print(f"❌ Arquivo de configuração não encontrado: {config_file}")
        return None
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Inicializa sistema
    print("🤖 Inicializando sistema multi-agente...")
    system = ObsidianMultiAgentSystem()
    
    # Verifica e carrega cofres
    cofres_carregados = []
    cofres_com_erro = []
    
    print(f"\n📁 Verificando {len(config['vaults'])} cofres...")
    
    for vault_config in config['vaults']:
        nome = vault_config['name']
        caminho = vault_config['path']
        descricao = vault_config['description']
        
        print(f"\n🔍 Verificando: {nome}")
        print(f"   Caminho: {caminho}")
        
        # Verifica se o caminho existe
        existe, mensagem = verificar_caminho(caminho)
        
        if existe:
            print(f"   {mensagem}")
            
            # Tenta adicionar o cofre
            try:
                sucesso = system.add_vault(nome, caminho, descricao)
                if sucesso:
                    cofres_carregados.append(nome)
                    print(f"   ✅ Cofre '{nome}' carregado com sucesso!")
                else:
                    cofres_com_erro.append((nome, "Erro ao adicionar cofre"))
                    print(f"   ❌ Erro ao carregar cofre '{nome}'")
            except Exception as e:
                cofres_com_erro.append((nome, str(e)))
                print(f"   ❌ Erro: {e}")
        else:
            cofres_com_erro.append((nome, mensagem))
            print(f"   ❌ {mensagem}")
    
    # Relatório final
    print("\n" + "=" * 50)
    print("📊 RELATÓRIO FINAL")
    print("=" * 50)
    
    print(f"\n✅ COFRES CARREGADOS ({len(cofres_carregados)}):")
    for cofre in cofres_carregados:
        print(f"   • {cofre}")
    
    if cofres_com_erro:
        print(f"\n❌ COFRES COM ERRO ({len(cofres_com_erro)}):")
        for cofre, erro in cofres_com_erro:
            print(f"   • {cofre}: {erro}")
    
    # Indexa cofres carregados
    if cofres_carregados:
        print(f"\n🔄 Indexando {len(cofres_carregados)} cofres...")
        try:
            await system.index_all_vaults()
            print("✅ Indexação concluída com sucesso!")
        except Exception as e:
            print(f"❌ Erro na indexação: {e}")
            return None
    else:
        print("\n⚠️ Nenhum cofre foi carregado. Verifique os caminhos.")
        return None
    
    # Estatísticas finais
    stats = system.get_vault_stats()
    print(f"\n📈 ESTATÍSTICAS:")
    print(f"   • Total de cofres: {stats['total_vaults']}")
    print(f"   • Total de arquivos: {stats['total_files']}")
    
    print(f"\n🎉 SISTEMA PRONTO!")
    print(f"   • {len(cofres_carregados)} cofres carregados")
    print(f"   • {stats['total_files']} arquivos indexados")
    print(f"   • Sistema multi-agente ativo")
    
    return system

async def testar_sistema(system):
    """Testa o sistema com algumas perguntas"""
    if not system:
        print("❌ Sistema não foi carregado corretamente")
        return
    
    print("\n🧪 TESTANDO SISTEMA")
    print("=" * 30)
    
    perguntas_teste = [
        "Quantos cofres foram carregados?",
        "Quais são as principais áreas do direito nos meus cofres?",
        "Encontre conexões entre direito penal e processo penal",
        "Sugira melhorias para organizar meus estudos jurídicos"
    ]
    
    for i, pergunta in enumerate(perguntas_teste, 1):
        print(f"\n{i}. 💬 {pergunta}")
        try:
            resposta = await system.chat(pergunta)
            print(f"🤖 {resposta[:200]}...")
        except Exception as e:
            print(f"❌ Erro: {e}")

async def main():
    """Função principal"""
    try:
        # Carrega cofres
        system = await carregar_cofres_alexandre()
        
        # Pergunta se quer testar
        if system:
            print("\n" + "=" * 50)
            resposta = input("🧪 Deseja testar o sistema? (s/n): ").strip().lower()
            
            if resposta in ['s', 'sim', 'y', 'yes']:
                await testar_sistema(system)
            
            print("\n💡 COMO USAR:")
            print("   1. Execute: streamlit run web_interface.py")
            print("   2. Ou use: python example_usage.py")
            print("   3. Ou importe: from carregar_cofres_alexandre import carregar_cofres_alexandre")
            
            print(f"\n🎯 Seus {system.get_vault_stats()['total_vaults']} cofres estão prontos para uso!")
        
    except KeyboardInterrupt:
        print("\n\n👋 Programa interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro geral: {e}")

if __name__ == "__main__":
    asyncio.run(main())
