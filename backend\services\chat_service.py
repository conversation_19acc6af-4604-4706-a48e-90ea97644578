import uuid
from typing import List, Dict, Optional
from datetime import datetime

from .search_service import search_service
from .note_editor_service import note_editor_service
from .command_analyzer import command_analyzer, CommandType
from ..models.gemini_client import gemini_client
from ..utils.database import db_manager
from ..utils.config import config

class ChatService:
    """Serviço principal do chat inteligente"""

    def __init__(self):
        self.search_service = search_service
        self.gemini_client = gemini_client
        self.db_manager = db_manager

    async def process_message(self, message: str, session_id: str = None) -> Dict:
        """
        Processa uma mensagem do usuário e gera resposta

        Args:
            message: Mensagem do usuário
            session_id: ID da sessão (opcional, será criado se não fornecido)

        Returns:
            Dict com resposta e metadados
        """
        try:
            # Gera session_id se não fornecido
            if not session_id:
                session_id = str(uuid.uuid4())

            # Obtém ou cria conversa
            conversation_id = self.db_manager.get_conversation_id(session_id)
            if not conversation_id:
                conversation_id = self.db_manager.create_conversation(session_id)

            # Salva mensagem do usuário
            self.db_manager.add_message(
                conversation_id=conversation_id,
                role='user',
                content=message,
                metadata={'timestamp': datetime.now().isoformat()}
            )

            # NOVA FUNCIONALIDADE: Analisa se é um comando de edição
            command_analysis = command_analyzer.analyze_command(message)

            if command_analysis['command_type'] == CommandType.MODIFY_NOTE:
                response = await self._handle_modify_note_command(command_analysis)
            elif command_analysis['command_type'] == CommandType.CREATE_NOTE:
                response = await self._handle_create_note_command(command_analysis)
            elif command_analysis['command_type'] == CommandType.ADD_TO_NOTE:
                response = await self._handle_add_to_note_command(command_analysis)
            else:
                # Chat normal
                response = await self._handle_chat_command(message, session_id)

            # Salva resposta do assistente
            self.db_manager.add_message(
                conversation_id=conversation_id,
                role='assistant',
                content=response['content'],
                metadata={
                    'command_type': command_analysis['command_type'].value,
                    'context_sources': response.get('sources', []),
                    'timestamp': datetime.now().isoformat()
                }
            )

            return {
                'session_id': session_id,
                'response': response['content'],
                'sources': response.get('sources', []),
                'context_results': response.get('context_results', []),
                'conversation_id': conversation_id,
                'command_type': command_analysis['command_type'].value,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            error_response = f"Desculpe, ocorreu um erro ao processar sua mensagem: {str(e)}"

            return {
                'session_id': session_id or str(uuid.uuid4()),
                'response': error_response,
                'sources': [],
                'context_results': [],
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    async def _handle_modify_note_command(self, command_analysis: Dict) -> Dict:
        """Lida com comandos de modificação de nota"""
        params = command_analysis['parameters']

        result = await note_editor_service.modify_note(
            note_title=params['note_title'],
            modification_request=params['modification_type'],
            user_message=command_analysis['original_message']
        )

        if result['success']:
            response_text = f"✅ {result['message']}\n\n"
            if 'changes_summary' in result:
                response_text += f"**Resumo das mudanças:** {result['changes_summary']}\n\n"
            response_text += f"📁 Arquivo: `{result['note_path']}`"
            if 'backup_path' in result:
                response_text += f"\n💾 Backup salvo em: `{result['backup_path']}`"
        else:
            response_text = f"❌ {result['message']}"
            if 'suggestions' in result and result['suggestions']:
                response_text += f"\n\n💡 **Notas similares encontradas:**\n"
                for suggestion in result['suggestions']:
                    response_text += f"- {suggestion}\n"

        return {
            'content': response_text,
            'sources': [],
            'note_operation': result
        }

    async def _handle_create_note_command(self, command_analysis: Dict) -> Dict:
        """Lida com comandos de criação de nota"""
        params = command_analysis['parameters']

        result = await note_editor_service.create_note(
            note_title=params['note_title'],
            content_request=params['content_type'],
            user_message=command_analysis['original_message']
        )

        if result['success']:
            response_text = f"✅ {result['message']}\n\n"
            response_text += f"📁 Arquivo criado: `{result['note_path']}`\n\n"
            if 'content_preview' in result:
                response_text += f"**Preview do conteúdo:**\n{result['content_preview']}"
        else:
            response_text = f"❌ {result['message']}"
            if 'existing_path' in result:
                response_text += f"\n📁 Arquivo existente: `{result['existing_path']}`"

        return {
            'content': response_text,
            'sources': [],
            'note_operation': result
        }

    async def _handle_add_to_note_command(self, command_analysis: Dict) -> Dict:
        """Lida com comandos de adição de conteúdo"""
        params = command_analysis['parameters']

        result = await note_editor_service.add_to_note(
            note_title=params['note_title'],
            section=params['target_section'],
            content_to_add=params['content_to_add'],
            user_message=command_analysis['original_message']
        )

        if result['success']:
            response_text = f"✅ {result['message']}\n\n"
            response_text += f"📁 Arquivo: `{result['note_path']}`\n"
            if 'added_content' in result:
                response_text += f"\n**Conteúdo adicionado:**\n{result['added_content']}"
            if 'backup_path' in result:
                response_text += f"\n💾 Backup salvo em: `{result['backup_path']}`"
        else:
            response_text = f"❌ {result['message']}"

        return {
            'content': response_text,
            'sources': [],
            'note_operation': result
        }

    async def _handle_chat_command(self, message: str, session_id: str) -> Dict:
        """Lida com chat normal (sem edição de notas)"""
        # Busca contexto relevante
        context_results = await self._get_relevant_context(message, session_id)

        # Obtém histórico da conversa
        conversation_history = self.db_manager.get_conversation_history(session_id)

        # Gera resposta com Gemini
        response = await self._generate_response(
            message, context_results, conversation_history
        )

        return {
            'content': response['content'],
            'sources': response['sources'],
            'context_results': context_results
        }

    async def _get_relevant_context(self, message: str, session_id: str) -> List[Dict]:
        """Busca contexto relevante para a mensagem"""
        try:
            # Analisa o tipo de consulta
            query_type = self._analyze_query_type(message)

            context_results = []

            if query_type == 'semantic':
                # Busca semântica padrão
                results = await self.search_service.hybrid_search(message, top_k=5)
                context_results.extend(results)

            elif query_type == 'category':
                # Busca por categoria
                category = self._extract_category_from_query(message)
                if category:
                    results = await self.search_service.search_by_category(category, top_k=5)
                    context_results.extend(results)

            elif query_type == 'recent':
                # Busca documentos recentes
                days = self._extract_time_period_from_query(message)
                results = await self.search_service.search_recent_documents(days, top_k=5)
                context_results.extend(results)

            elif query_type == 'tags':
                # Busca por tags
                tags = self._extract_tags_from_query(message)
                if tags:
                    results = await self.search_service.search_by_tags(tags, top_k=5)
                    context_results.extend(results)

            # Se não encontrou resultados específicos, faz busca semântica geral
            if not context_results:
                results = await self.search_service.semantic_search(message, top_k=5)
                context_results.extend(results)

            return context_results

        except Exception as e:
            print(f"Erro ao buscar contexto: {e}")
            return []

    def _analyze_query_type(self, message: str) -> str:
        """Analisa o tipo de consulta baseado na mensagem"""
        message_lower = message.lower()

        # Palavras-chave para diferentes tipos de busca
        category_keywords = ['direito', 'neurociência', 'marketing', 'tecnologia', 'categoria']
        recent_keywords = ['recente', 'último', 'nova', 'hoje', 'ontem', 'semana', 'mês']
        tag_keywords = ['tag', 'marcado', 'etiqueta']

        if any(keyword in message_lower for keyword in category_keywords):
            return 'category'
        elif any(keyword in message_lower for keyword in recent_keywords):
            return 'recent'
        elif any(keyword in message_lower for keyword in tag_keywords):
            return 'tags'
        else:
            return 'semantic'

    def _extract_category_from_query(self, message: str) -> Optional[str]:
        """Extrai categoria da consulta"""
        message_lower = message.lower()
        categories = ['direito', 'neurociência', 'marketing', 'tecnologia', 'estudos', 'trabalho']

        for category in categories:
            if category in message_lower:
                return category

        return None

    def _extract_time_period_from_query(self, message: str) -> int:
        """Extrai período de tempo da consulta (em dias)"""
        message_lower = message.lower()

        if 'hoje' in message_lower:
            return 1
        elif 'ontem' in message_lower:
            return 2
        elif 'semana' in message_lower:
            return 7
        elif 'mês' in message_lower:
            return 30
        else:
            return 7  # Padrão: última semana

    def _extract_tags_from_query(self, message: str) -> List[str]:
        """Extrai tags da consulta"""
        # Implementação simples - pode ser melhorada
        import re

        # Procura por padrões como #tag ou "tag"
        tags = re.findall(r'#(\w+)', message)
        quoted_tags = re.findall(r'"([^"]+)"', message)

        return tags + quoted_tags

    async def _generate_response(self, message: str, context_results: List[Dict],
                               conversation_history: List[Dict]) -> Dict:
        """Gera resposta usando Gemini com contexto"""
        try:
            # Prepara contexto das notas
            context_text = self._format_context_for_prompt(context_results)

            # Gera resposta
            response_content = await self.gemini_client.generate_response(
                prompt=message,
                context=context_text,
                conversation_history=conversation_history
            )

            # Prepara fontes
            sources = self._extract_sources_from_context(context_results)

            return {
                'content': response_content,
                'sources': sources
            }

        except Exception as e:
            return {
                'content': f"Desculpe, não consegui gerar uma resposta adequada. Erro: {str(e)}",
                'sources': []
            }

    def _format_context_for_prompt(self, context_results: List[Dict]) -> str:
        """Formata contexto para o prompt do Gemini"""
        if not context_results:
            return "Nenhum contexto relevante encontrado nas notas."

        context_parts = []

        for i, result in enumerate(context_results, 1):
            title = result.get('document_title', 'Documento sem título')
            content = result.get('chunk_content', '')
            similarity = result.get('similarity_score', 0)

            context_part = f"""
NOTA {i}: {title}
Relevância: {similarity:.2f}
Conteúdo:
{content}
---
"""
            context_parts.append(context_part)

        return '\n'.join(context_parts)

    def _extract_sources_from_context(self, context_results: List[Dict]) -> List[Dict]:
        """Extrai informações das fontes para referência"""
        sources = []

        for result in context_results:
            source = {
                'title': result.get('document_title', 'Documento sem título'),
                'path': result.get('document_path', ''),
                'chunk_index': result.get('chunk_index', 0),
                'similarity_score': result.get('similarity_score', 0),
                'search_type': result.get('search_type', 'semantic')
            }
            sources.append(source)

        return sources

    async def get_conversation_history(self, session_id: str) -> List[Dict]:
        """Obtém histórico completo da conversa"""
        return self.db_manager.get_conversation_history(session_id)

    async def clear_conversation(self, session_id: str) -> bool:
        """Limpa histórico da conversa"""
        try:
            conversation_id = self.db_manager.get_conversation_id(session_id)
            if conversation_id:
                # Cria nova conversa com mesmo session_id
                self.db_manager.create_conversation(session_id)
                return True
            return False
        except Exception as e:
            print(f"Erro ao limpar conversa: {e}")
            return False

    async def get_chat_statistics(self) -> Dict:
        """Obtém estatísticas do chat"""
        try:
            # Estatísticas básicas do banco
            indexed_docs = self.db_manager.get_indexed_documents()

            return {
                'total_indexed_documents': len(indexed_docs),
                'index_stats': search_service.indexing_service.get_index_stats(),
                'recent_documents': indexed_docs[:5]  # 5 mais recentes
            }
        except Exception as e:
            return {'error': str(e)}

# Instância global do serviço de chat
chat_service = ChatService()
