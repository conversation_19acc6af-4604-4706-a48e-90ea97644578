#!/usr/bin/env python3
"""
Sistema AUTOMÁTICO: LLM Local + Gemini SEMPRE JUNTOS
Sem escolhas - colaboração automática constante
"""

import asyncio
import json
import os
import time
import re
from pathlib import Path
from typing import List, Dict, Any
from collections import Counter

# Configuração
GOOGLE_API_KEY = "AIzaSyCq1rBFLFqX04jQ2ejd5Tkbane-lTvIsVU"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

class ProcessadorLocal:
    """Sempre processa primeiro - sem escolhas"""
    
    def __init__(self):
        self.ativo = True
        print("⚡ Processador Local: SEMPRE ATIVO")
    
    def extrair_informacoes(self, texto: str) -> Dict[str, Any]:
        """Extrai informações básicas SEMPRE"""
        if not texto:
            return {}
        
        # Análise automática básica
        info = {
            'tamanho': len(texto),
            'palavras': len(texto.split()),
            'paragrafos': len(texto.split('\n\n')),
            'topicos_juridicos': self._encontrar_topicos_juridicos(texto),
            'categorias': self._identificar_categorias(texto),
            'artigos_mencionados': self._extrair_artigos(texto),
            'palavras_chave': self._extrair_palavras_chave(texto),
            'resumo_estrutural': self._resumo_estrutural(texto)
        }
        
        return info
    
    def _encontrar_topicos_juridicos(self, texto: str) -> List[str]:
        """Encontra tópicos jurídicos automaticamente"""
        topicos_juridicos = [
            'direito penal', 'direito civil', 'direito constitucional',
            'processo penal', 'processo civil', 'direito do trabalho',
            'direito tributário', 'direito administrativo', 'direito empresarial',
            'homicídio', 'furto', 'roubo', 'estelionato', 'contrato',
            'responsabilidade civil', 'danos morais', 'prescrição',
            'princípio', 'jurisprudência', 'súmula', 'precedente'
        ]
        
        texto_lower = texto.lower()
        encontrados = []
        
        for topico in topicos_juridicos:
            if topico in texto_lower:
                encontrados.append(topico)
        
        return encontrados[:10]  # Máximo 10
    
    def _identificar_categorias(self, texto: str) -> List[str]:
        """Identifica categorias automaticamente"""
        categorias = {
            'penal': ['crime', 'penal', 'delito', 'pena', 'código penal'],
            'civil': ['civil', 'contrato', 'responsabilidade', 'danos'],
            'constitucional': ['constituição', 'constitucional', 'direitos fundamentais'],
            'processual': ['processo', 'procedimento', 'recurso', 'sentença'],
            'trabalhista': ['trabalho', 'trabalhista', 'clt', 'empregado'],
            'tributário': ['tributo', 'imposto', 'fiscal', 'icms', 'ipi'],
            'administrativo': ['administrativo', 'servidor público', 'licitação']
        }
        
        texto_lower = texto.lower()
        cats_encontradas = []
        
        for categoria, palavras in categorias.items():
            if any(palavra in texto_lower for palavra in palavras):
                cats_encontradas.append(categoria)
        
        return cats_encontradas
    
    def _extrair_artigos(self, texto: str) -> List[str]:
        """Extrai artigos mencionados"""
        # Padrões para artigos
        padroes = [
            r'art\.?\s*(\d+)',
            r'artigo\s*(\d+)',
            r'art\.?\s*(\d+)º?',
        ]
        
        artigos = []
        for padrao in padroes:
            matches = re.findall(padrao, texto.lower())
            artigos.extend([f"art. {match}" for match in matches])
        
        return list(set(artigos))[:5]  # Máximo 5, sem duplicatas
    
    def _extrair_palavras_chave(self, texto: str) -> List[str]:
        """Extrai palavras-chave importantes"""
        # Palavras jurídicas importantes
        palavras_importantes = [
            'direito', 'lei', 'código', 'constituição', 'crime', 'processo',
            'tribunal', 'juiz', 'advogado', 'réu', 'autor', 'sentença',
            'recurso', 'apelação', 'cassação', 'habeas corpus', 'mandado'
        ]
        
        texto_lower = texto.lower()
        palavras_encontradas = []
        
        for palavra in palavras_importantes:
            if palavra in texto_lower:
                palavras_encontradas.append(palavra)
        
        return palavras_encontradas[:8]  # Máximo 8
    
    def _resumo_estrutural(self, texto: str) -> str:
        """Cria resumo estrutural básico"""
        linhas = texto.split('\n')
        
        # Identifica estrutura
        titulos = [linha for linha in linhas if linha.startswith('#') or linha.isupper()]
        
        if titulos:
            return f"Documento estruturado com {len(titulos)} seções principais"
        else:
            return f"Texto corrido com {len(linhas)} linhas"

class AnalistaGemini:
    """Sempre analisa depois do Local - sem escolhas"""
    
    def __init__(self):
        self.llm = None
        self.ativo = False
        self._carregar()
    
    def _carregar(self):
        """Carrega Gemini automaticamente"""
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            self.llm = ChatGoogleGenerativeAI(
                model="gemini-1.5-pro",
                temperature=0.3,
                google_api_key=GOOGLE_API_KEY
            )
            
            self.ativo = True
            print("🧠 Analista Gemini: SEMPRE ATIVO")
            
        except Exception as e:
            print(f"⚠️ Gemini: {e}")
    
    async def analisar_com_preprocessamento(self, pergunta: str, info_local: Dict, documentos: List[Dict]) -> str:
        """Sempre analisa usando pré-processamento local"""
        if not self.ativo:
            return "❌ Gemini não disponível"
        
        # Monta contexto enriquecido AUTOMATICAMENTE
        contexto_enriquecido = self._montar_contexto(info_local, documentos)
        
        prompt = f"""
ANÁLISE PRÉVIA AUTOMÁTICA:
{contexto_enriquecido}

PERGUNTA DO USUÁRIO: {pergunta}

Como especialista jurídico, analise considerando:
1. O pré-processamento automático feito
2. Os documentos e informações extraídas
3. Seu conhecimento especializado

Forneça resposta completa, clara e bem estruturada em português.
"""
        
        try:
            from langchain.schema import HumanMessage
            
            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            return response.content
            
        except Exception as e:
            return f"❌ Erro na análise: {e}"
    
    def _montar_contexto(self, info_local: Dict, documentos: List[Dict]) -> str:
        """Monta contexto automaticamente"""
        contexto = []
        
        # Informações do processamento local
        if info_local:
            contexto.append("📊 ANÁLISE ESTRUTURAL:")
            contexto.append(f"- Tamanho: {info_local.get('palavras', 0)} palavras")
            contexto.append(f"- Categorias: {', '.join(info_local.get('categorias', []))}")
            contexto.append(f"- Tópicos: {', '.join(info_local.get('topicos_juridicos', [])[:5])}")
            contexto.append(f"- Artigos: {', '.join(info_local.get('artigos_mencionados', []))}")
            contexto.append(f"- Estrutura: {info_local.get('resumo_estrutural', '')}")
            contexto.append("")
        
        # Conteúdo dos documentos
        if documentos:
            contexto.append("📄 DOCUMENTOS RELEVANTES:")
            for i, doc in enumerate(documentos[:3], 1):
                fonte = doc.get('metadata', {}).get('filename', f'doc_{i}')
                conteudo = doc.get('content', '')[:400]
                contexto.append(f"\n{i}. {fonte}:")
                contexto.append(conteudo + "...")
                contexto.append("")
        
        return "\n".join(contexto)

class SistemaAutomatico:
    """Sistema que SEMPRE usa Local + Gemini juntos"""
    
    def __init__(self):
        print("🤝 SISTEMA AUTOMÁTICO INICIALIZANDO...")
        print("=" * 45)
        print("Local + Gemini SEMPRE trabalhando juntos!")
        print()
        
        # Inicializa componentes (sempre os dois)
        self.local = ProcessadorLocal()
        self.gemini = AnalistaGemini()
        
        # Status
        print(f"⚡ Processador Local: ✅ SEMPRE ATIVO")
        print(f"🧠 Analista Gemini: {'✅ ATIVO' if self.gemini.ativo else '❌ INATIVO'}")
        print()
        print("🔄 FLUXO AUTOMÁTICO:")
        print("   1. Local pré-processa SEMPRE")
        print("   2. Gemini analisa SEMPRE")
        print("   3. Resposta combinada SEMPRE")
        print()
        print("✅ SISTEMA AUTOMÁTICO PRONTO!")
    
    async def processar_automatico(self, pergunta: str, documentos: List[Dict] = None) -> str:
        """Processamento AUTOMÁTICO - sempre os dois juntos"""
        
        print(f"🤝 PROCESSAMENTO AUTOMÁTICO: {pergunta}")
        
        if not documentos:
            documentos = []
        
        inicio = time.time()
        
        # ETAPA 1: Processador Local SEMPRE trabalha primeiro
        print("⚡ Etapa 1: Processador Local analisando...")
        
        # Combina todo o texto dos documentos
        texto_completo = "\n".join([doc.get('content', '') for doc in documentos])
        
        # Pré-processamento automático
        info_local = self.local.extrair_informacoes(texto_completo)
        
        tempo_local = time.time() - inicio
        print(f"✅ Pré-processamento em {tempo_local:.1f}s")
        
        # ETAPA 2: Gemini SEMPRE analisa depois
        if self.gemini.ativo:
            print("🧠 Etapa 2: Gemini analisando com contexto...")
            
            resposta = await self.gemini.analisar_com_preprocessamento(pergunta, info_local, documentos)
            
            tempo_total = time.time() - inicio
            
            # Resposta combinada AUTOMÁTICA
            return f"""🤝 [AUTOMÁTICO] Processado em {tempo_total:.1f}s

{resposta}

---
⚡ Pré-processamento: {tempo_local:.1f}s | 🧠 Análise: {tempo_total - tempo_local:.1f}s
📊 Categorias: {', '.join(info_local.get('categorias', []))}
🔍 Tópicos: {len(info_local.get('topicos_juridicos', []))} identificados
"""
        
        else:
            # Fallback: só processamento local
            return f"""⚡ [LOCAL] Análise básica:

📊 Informações extraídas:
- Categorias: {', '.join(info_local.get('categorias', []))}
- Tópicos: {', '.join(info_local.get('topicos_juridicos', []))}
- Artigos: {', '.join(info_local.get('artigos_mencionados', []))}
- Palavras-chave: {', '.join(info_local.get('palavras_chave', []))}

⚠️ Gemini não disponível para análise completa.
"""

async def demo_automatico():
    """Demo do sistema automático"""
    print("🎯 DEMO SISTEMA AUTOMÁTICO")
    print("=" * 30)
    
    # Inicializa sistema
    sistema = SistemaAutomatico()
    
    # Documentos de teste
    docs_teste = [
        {
            'content': '''# Direito Penal - Homicídio

O crime de homicídio está previsto no art. 121 do Código Penal brasileiro.

## Elementos do tipo penal:
- Matar alguém
- Dolo ou culpa
- Resultado morte

A pena varia de 6 a 20 anos de reclusão para o homicídio simples.

### Qualificadoras (art. 121, §2º):
- Motivo fútil
- Meio cruel
- Recurso que dificulte a defesa da vítima''',
            'metadata': {'filename': 'homicidio.md', 'source': 'direito_penal'}
        },
        {
            'content': '''# Processo Penal - Princípios

## Presunção de Inocência
Previsto no art. 5º, LVII da Constituição Federal.

Ninguém será considerado culpado até o trânsito em julgado de sentença penal condenatória.

## Contraditório e Ampla Defesa
Art. 5º, LV da CF/88.

Garantias fundamentais do processo penal brasileiro.''',
            'metadata': {'filename': 'principios.md', 'source': 'processo_penal'}
        }
    ]
    
    # Perguntas de teste
    perguntas = [
        "O que é homicídio no direito penal brasileiro?",
        "Explique o princípio da presunção de inocência",
        "Quais são as qualificadoras do homicídio?",
        "Como funciona o contraditório no processo penal?"
    ]
    
    for pergunta in perguntas:
        print(f"\n{'='*70}")
        resposta = await sistema.processar_automatico(pergunta, docs_teste)
        print(resposta)
        print("\n" + "⏸️ Pressione Enter para continuar...")
        input()

async def main():
    """Execução automática"""
    print("🤝 SISTEMA AUTOMÁTICO OBSIDIAN")
    print("=" * 35)
    print("Local + Gemini SEMPRE juntos, sem escolhas!")
    print()
    
    print("Opções:")
    print("1. 🧪 Demo automático")
    print("2. 💬 Chat automático")
    print("3. 🌐 Interface web")
    
    try:
        choice = input("\nEscolha: ").strip()
        
        if choice == "1":
            await demo_automatico()
        elif choice == "2":
            await chat_automatico()
        elif choice == "3":
            print("🌐 Abrindo interface...")
            os.system("streamlit run web_interface.py")
        else:
            print("❌ Opção inválida")
            
    except KeyboardInterrupt:
        print("\n👋 Sistema encerrado")

async def chat_automatico():
    """Chat que sempre usa os dois juntos"""
    print("💬 CHAT AUTOMÁTICO")
    print("=" * 20)
    print("Digite 'sair' para terminar")
    print("Sistema SEMPRE usa Local + Gemini juntos!")
    
    sistema = SistemaAutomatico()
    
    while True:
        try:
            pergunta = input("\n💬 Você: ").strip()
            
            if pergunta.lower() in ['sair', 'exit', 'quit']:
                print("👋 Até logo!")
                break
            
            if not pergunta:
                continue
            
            # SEMPRE processa com os dois
            resposta = await sistema.processar_automatico(pergunta)
            print(f"\n🤖 {resposta}")
            
        except KeyboardInterrupt:
            print("\n👋 Chat encerrado")
            break

if __name__ == "__main__":
    asyncio.run(main())
