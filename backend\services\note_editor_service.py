import os
import re
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import frontmatter

from ..models.document_processor import document_processor
from ..models.gemini_client import gemini_client
from ..services.indexing_service import indexing_service
from ..utils.config import config

class NoteEditorService:
    """Serviço para edição e criação de notas do Obsidian"""
    
    def __init__(self):
        self.vault_path = config.OBSIDIAN_VAULT_PATH
        self.document_processor = document_processor
        self.gemini_client = gemini_client
        self.indexing_service = indexing_service
    
    async def modify_note(self, note_title: str, modification_request: str, 
                         user_message: str) -> Dict:
        """
        Modifica uma nota existente baseado na solicitação do usuário
        
        Args:
            note_title: Título da nota a ser modificada
            modification_request: Tipo de modificação (adicionar, atualizar, corrigir)
            user_message: Mensagem completa do usuário
            
        Returns:
            Dict com resultado da modificação
        """
        try:
            # Encontra a nota
            note_path = self._find_note_by_title(note_title)
            if not note_path:
                return {
                    'success': False,
                    'message': f"Nota '{note_title}' não encontrada",
                    'suggestions': self._suggest_similar_notes(note_title)
                }
            
            # Lê o conteúdo atual
            current_content = self._read_note(note_path)
            if not current_content:
                return {
                    'success': False,
                    'message': f"Erro ao ler a nota '{note_title}'"
                }
            
            # Gera a modificação usando Gemini
            modified_content = await self._generate_modification(
                current_content, modification_request, user_message
            )
            
            # Salva a nota modificada
            backup_path = self._create_backup(note_path)
            success = self._save_note(note_path, modified_content)
            
            if success:
                # Reindexiza a nota modificada
                await self.indexing_service.index_document(note_path)
                
                return {
                    'success': True,
                    'message': f"Nota '{note_title}' modificada com sucesso",
                    'note_path': str(note_path),
                    'backup_path': str(backup_path),
                    'changes_summary': await self._summarize_changes(current_content, modified_content)
                }
            else:
                return {
                    'success': False,
                    'message': f"Erro ao salvar modificações na nota '{note_title}'"
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f"Erro ao modificar nota: {str(e)}"
            }
    
    async def create_note(self, note_title: str, content_request: str, 
                         user_message: str) -> Dict:
        """
        Cria uma nova nota baseada na solicitação do usuário
        
        Args:
            note_title: Título da nova nota
            content_request: Tipo de conteúdo solicitado
            user_message: Mensagem completa do usuário
            
        Returns:
            Dict com resultado da criação
        """
        try:
            # Verifica se a nota já existe
            existing_path = self._find_note_by_title(note_title)
            if existing_path:
                return {
                    'success': False,
                    'message': f"Nota '{note_title}' já existe. Use 'modificar' para editá-la.",
                    'existing_path': str(existing_path)
                }
            
            # Gera o conteúdo usando Gemini
            note_content = await self._generate_new_note_content(
                note_title, content_request, user_message
            )
            
            # Cria o arquivo da nota
            note_path = self.vault_path / f"{self._sanitize_filename(note_title)}.md"
            success = self._save_note(note_path, note_content)
            
            if success:
                # Indexa a nova nota
                await self.indexing_service.index_document(note_path)
                
                return {
                    'success': True,
                    'message': f"Nova nota '{note_title}' criada com sucesso",
                    'note_path': str(note_path),
                    'content_preview': note_content[:200] + "..."
                }
            else:
                return {
                    'success': False,
                    'message': f"Erro ao criar a nota '{note_title}'"
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f"Erro ao criar nota: {str(e)}"
            }
    
    async def add_to_note(self, note_title: str, section: str, 
                         content_to_add: str, user_message: str) -> Dict:
        """
        Adiciona conteúdo a uma seção específica da nota
        
        Args:
            note_title: Título da nota
            section: Seção onde adicionar (ou "final" para o fim)
            content_to_add: Conteúdo a ser adicionado
            user_message: Mensagem completa do usuário
            
        Returns:
            Dict com resultado da adição
        """
        try:
            # Encontra a nota
            note_path = self._find_note_by_title(note_title)
            if not note_path:
                return {
                    'success': False,
                    'message': f"Nota '{note_title}' não encontrada"
                }
            
            # Lê o conteúdo atual
            current_content = self._read_note(note_path)
            
            # Gera o conteúdo a ser adicionado usando Gemini
            generated_content = await self._generate_addition(
                current_content, section, content_to_add, user_message
            )
            
            # Adiciona o conteúdo na posição correta
            modified_content = self._insert_content(current_content, section, generated_content)
            
            # Salva a nota modificada
            backup_path = self._create_backup(note_path)
            success = self._save_note(note_path, modified_content)
            
            if success:
                # Reindexiza a nota
                await self.indexing_service.index_document(note_path)
                
                return {
                    'success': True,
                    'message': f"Conteúdo adicionado à nota '{note_title}' com sucesso",
                    'note_path': str(note_path),
                    'backup_path': str(backup_path),
                    'added_content': generated_content[:200] + "..."
                }
            else:
                return {
                    'success': False,
                    'message': f"Erro ao adicionar conteúdo à nota '{note_title}'"
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f"Erro ao adicionar conteúdo: {str(e)}"
            }
    
    def _find_note_by_title(self, title: str) -> Optional[Path]:
        """Encontra uma nota pelo título"""
        # Busca exata
        exact_match = self.vault_path / f"{title}.md"
        if exact_match.exists():
            return exact_match
        
        # Busca por similaridade
        for md_file in self.vault_path.glob("*.md"):
            if title.lower() in md_file.stem.lower():
                return md_file
        
        return None
    
    def _suggest_similar_notes(self, title: str) -> List[str]:
        """Sugere notas similares"""
        suggestions = []
        title_lower = title.lower()
        
        for md_file in self.vault_path.glob("*.md"):
            if any(word in md_file.stem.lower() for word in title_lower.split()):
                suggestions.append(md_file.stem)
        
        return suggestions[:5]
    
    def _read_note(self, note_path: Path) -> Optional[str]:
        """Lê o conteúdo de uma nota"""
        try:
            with open(note_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            return None
    
    def _save_note(self, note_path: Path, content: str) -> bool:
        """Salva uma nota"""
        try:
            note_path.parent.mkdir(parents=True, exist_ok=True)
            with open(note_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception:
            return False
    
    def _create_backup(self, note_path: Path) -> Path:
        """Cria backup da nota antes de modificar"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = self.vault_path / ".backups"
        backup_dir.mkdir(exist_ok=True)
        
        backup_path = backup_dir / f"{note_path.stem}_{timestamp}.md"
        
        try:
            content = self._read_note(note_path)
            if content:
                self._save_note(backup_path, content)
        except Exception:
            pass
        
        return backup_path
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitiza nome do arquivo"""
        # Remove caracteres inválidos
        sanitized = re.sub(r'[<>:"/\\|?*]', '', filename)
        # Remove espaços extras
        sanitized = re.sub(r'\s+', ' ', sanitized).strip()
        return sanitized
    
    def _insert_content(self, current_content: str, section: str, new_content: str) -> str:
        """Insere conteúdo na posição correta"""
        if section.lower() == "final" or section.lower() == "fim":
            return current_content + "\n\n" + new_content
        
        # Procura pela seção
        lines = current_content.split('\n')
        section_pattern = rf"^#+\s*{re.escape(section)}"
        
        for i, line in enumerate(lines):
            if re.match(section_pattern, line, re.IGNORECASE):
                # Encontra o final da seção
                next_section_idx = len(lines)
                for j in range(i + 1, len(lines)):
                    if re.match(r'^#+\s', lines[j]):
                        next_section_idx = j
                        break
                
                # Insere o conteúdo
                lines.insert(next_section_idx, "\n" + new_content)
                return '\n'.join(lines)
        
        # Se não encontrou a seção, adiciona no final
        return current_content + f"\n\n## {section}\n\n{new_content}"
    
    async def _generate_modification(self, current_content: str, modification_request: str, 
                                   user_message: str) -> str:
        """Gera modificação usando Gemini"""
        prompt = f"""Você é um assistente especializado em editar notas do Obsidian.

CONTEÚDO ATUAL DA NOTA:
{current_content}

SOLICITAÇÃO DO USUÁRIO:
{user_message}

TIPO DE MODIFICAÇÃO:
{modification_request}

INSTRUÇÕES:
1. Modifique o conteúdo da nota conforme solicitado
2. Mantenha o formato Markdown
3. Preserve o frontmatter se existir
4. Mantenha a estrutura e estilo da nota
5. Adicione/modifique apenas o que foi solicitado
6. Retorne APENAS o conteúdo completo modificado da nota

NOTA MODIFICADA:"""

        try:
            response = await self.gemini_client.generate_response(prompt)
            return response.strip()
        except Exception as e:
            return current_content  # Retorna original se houver erro
    
    async def _generate_new_note_content(self, title: str, content_request: str, 
                                       user_message: str) -> str:
        """Gera conteúdo para nova nota usando Gemini"""
        prompt = f"""Você é um assistente especializado em criar notas do Obsidian.

TÍTULO DA NOVA NOTA: {title}

SOLICITAÇÃO DO USUÁRIO:
{user_message}

TIPO DE CONTEÚDO:
{content_request}

INSTRUÇÕES:
1. Crie uma nota completa e bem estruturada
2. Use formato Markdown apropriado
3. Inclua frontmatter com metadados relevantes
4. Organize o conteúdo em seções lógicas
5. Adicione tags relevantes
6. Mantenha um estilo acadêmico e informativo

NOVA NOTA:"""

        try:
            response = await self.gemini_client.generate_response(prompt)
            
            # Adiciona frontmatter se não existir
            if not response.startswith('---'):
                frontmatter_content = f"""---
title: "{title}"
created: {datetime.now().strftime('%Y-%m-%d')}
tags: []
---

"""
                response = frontmatter_content + response
            
            return response.strip()
        except Exception as e:
            # Retorna template básico se houver erro
            return f"""---
title: "{title}"
created: {datetime.now().strftime('%Y-%m-%d')}
tags: []
---

# {title}

Conteúdo da nota criado em {datetime.now().strftime('%d/%m/%Y às %H:%M')}.

## Introdução

## Desenvolvimento

## Conclusão
"""
    
    async def _generate_addition(self, current_content: str, section: str, 
                               content_to_add: str, user_message: str) -> str:
        """Gera conteúdo a ser adicionado usando Gemini"""
        prompt = f"""Você é um assistente especializado em expandir notas do Obsidian.

CONTEÚDO ATUAL DA NOTA:
{current_content}

SEÇÃO PARA ADICIONAR: {section}

SOLICITAÇÃO DO USUÁRIO:
{user_message}

CONTEÚDO A ADICIONAR:
{content_to_add}

INSTRUÇÕES:
1. Gere conteúdo relevante para adicionar à seção especificada
2. Use formato Markdown apropriado
3. Mantenha consistência com o estilo da nota
4. Seja informativo e bem estruturado
5. Retorne APENAS o conteúdo a ser adicionado (sem a nota completa)

CONTEÚDO A ADICIONAR:"""

        try:
            response = await self.gemini_client.generate_response(prompt)
            return response.strip()
        except Exception as e:
            return content_to_add  # Retorna original se houver erro
    
    async def _summarize_changes(self, old_content: str, new_content: str) -> str:
        """Gera resumo das mudanças feitas"""
        prompt = f"""Compare o conteúdo antigo e novo de uma nota e resuma as principais mudanças:

CONTEÚDO ANTIGO:
{old_content[:1000]}...

CONTEÚDO NOVO:
{new_content[:1000]}...

Resuma as principais mudanças em 2-3 frases:"""

        try:
            response = await self.gemini_client.generate_response(prompt)
            return response.strip()
        except Exception:
            return "Nota modificada com sucesso."

# Instância global do serviço de edição
note_editor_service = NoteEditorService()
