<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Chat Simples</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        #chat-messages {
            border: 1px solid #ccc;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 20px;
            background: #f9f9f9;
        }
        
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
        }
        
        .user-message {
            background: #007bff;
            color: white;
            text-align: right;
        }
        
        .assistant-message {
            background: #e9ecef;
            color: #333;
        }
        
        #input-container {
            display: flex;
            gap: 10px;
        }
        
        #message-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        
        #send-btn {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        #send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .error {
            color: red;
            font-weight: bold;
        }
        
        .loading {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>🧪 Teste Chat Simples</h1>
    
    <div id="chat-messages">
        <div class="message assistant-message">
            <strong>Assistente:</strong> Olá! Digite uma mensagem para testar o chat.
        </div>
    </div>
    
    <div id="input-container">
        <input type="text" id="message-input" placeholder="Digite sua mensagem..." />
        <button id="send-btn">Enviar</button>
    </div>
    
    <div style="margin-top: 20px;">
        <h3>Comandos de Teste:</h3>
        <button onclick="testMessage('Olá!')">Teste Simples</button>
        <button onclick="testMessage('Crie uma nova nota sobre Python')">Criar Nota</button>
        <button onclick="testMessage('Quais são os conceitos de Direito?')">Chat Normal</button>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        let currentSessionId = 'test_session_' + Date.now();
        
        const messagesContainer = document.getElementById('chat-messages');
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');
        
        // Event listeners
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        sendBtn.addEventListener('click', sendMessage);
        
        function addMessage(role, content, isError = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            
            if (isError) {
                messageDiv.classList.add('error');
            }
            
            messageDiv.innerHTML = `<strong>${role === 'user' ? 'Você' : 'Assistente'}:</strong> ${content}`;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function showLoading() {
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'message assistant-message loading';
            loadingDiv.id = 'loading-message';
            loadingDiv.innerHTML = '<strong>Assistente:</strong> Pensando...';
            
            messagesContainer.appendChild(loadingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        function hideLoading() {
            const loadingDiv = document.getElementById('loading-message');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }
        
        async function sendMessage() {
            const message = messageInput.value.trim();
            
            if (!message) {
                alert('Digite uma mensagem!');
                return;
            }
            
            // Desabilita input
            messageInput.disabled = true;
            sendBtn.disabled = true;
            
            // Adiciona mensagem do usuário
            addMessage('user', message);
            
            // Limpa input
            messageInput.value = '';
            
            // Mostra loading
            showLoading();
            
            try {
                console.log('Enviando mensagem:', message);
                
                const response = await fetch(`${API_BASE_URL}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId
                    })
                });
                
                console.log('Status da resposta:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('Resposta recebida:', result);
                
                // Remove loading
                hideLoading();
                
                // Adiciona resposta
                addMessage('assistant', result.response);
                
                // Mostra informações adicionais
                if (result.command_type && result.command_type !== 'chat') {
                    addMessage('assistant', `🔧 Comando detectado: ${result.command_type}`, false);
                }
                
                if (result.sources && result.sources.length > 0) {
                    addMessage('assistant', `📚 Fontes: ${result.sources.length} documentos encontrados`, false);
                }
                
            } catch (error) {
                console.error('Erro:', error);
                hideLoading();
                addMessage('assistant', `❌ Erro: ${error.message}`, true);
            } finally {
                // Reabilita input
                messageInput.disabled = false;
                sendBtn.disabled = false;
                messageInput.focus();
            }
        }
        
        function testMessage(message) {
            messageInput.value = message;
            sendMessage();
        }
        
        // Testa conexão ao carregar
        window.addEventListener('load', async function() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    const result = await response.json();
                    addMessage('assistant', `✅ Conexão OK! Status: ${result.status}`);
                } else {
                    addMessage('assistant', `⚠️ Servidor respondeu com status: ${response.status}`, true);
                }
            } catch (error) {
                addMessage('assistant', `❌ Erro de conexão: ${error.message}`, true);
            }
        });
    </script>
</body>
</html>
