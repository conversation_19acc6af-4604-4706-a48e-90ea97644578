import os
import hashlib
import frontmatter
import markdown
from pathlib import Path
from typing import List, Dict, Tuple
import re
from datetime import datetime
from ..utils.config import config

class DocumentProcessor:
    """Processador de documentos Markdown do Obsidian"""
    
    def __init__(self):
        self.vault_path = config.OBSIDIAN_VAULT_PATH
        self.chunk_size = config.CHUNK_SIZE
        self.chunk_overlap = config.CHUNK_OVERLAP
    
    def scan_vault(self) -> List[Path]:
        """Escaneia o vault do Obsidian em busca de arquivos .md"""
        if not self.vault_path.exists():
            return []
        
        md_files = []
        for file_path in self.vault_path.rglob("*.md"):
            if file_path.is_file():
                md_files.append(file_path)
        
        return md_files
    
    def get_file_hash(self, file_path: Path) -> str:
        """Calcula hash MD5 do arquivo para detectar mudanças"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()
        except Exception:
            return ""
    
    def parse_markdown_file(self, file_path: Path) -> Dict:
        """
        Processa um arquivo Markdown do Obsidian
        
        Returns:
            Dict com metadados e conteúdo processado
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                post = frontmatter.load(f)
            
            # Extrai metadados do frontmatter
            metadata = post.metadata
            content = post.content
            
            # Processa o conteúdo Markdown
            processed_content = self._process_markdown_content(content)
            
            # Extrai título (do frontmatter ou do primeiro heading)
            title = self._extract_title(metadata, processed_content, file_path)
            
            # Extrai tags
            tags = self._extract_tags(metadata, content)
            
            # Calcula estatísticas
            word_count = len(processed_content.split())
            
            return {
                'file_path': str(file_path),
                'title': title,
                'content': processed_content,
                'raw_content': content,
                'metadata': metadata,
                'tags': tags,
                'word_count': word_count,
                'file_hash': self.get_file_hash(file_path),
                'modified_time': datetime.fromtimestamp(file_path.stat().st_mtime),
                'relative_path': str(file_path.relative_to(self.vault_path))
            }
            
        except Exception as e:
            return {
                'file_path': str(file_path),
                'title': file_path.stem,
                'content': '',
                'error': str(e)
            }
    
    def _process_markdown_content(self, content: str) -> str:
        """Processa conteúdo Markdown removendo sintaxe desnecessária"""
        # Remove links internos do Obsidian [[link]]
        content = re.sub(r'\[\[([^\]]+)\]\]', r'\1', content)
        
        # Remove links externos [texto](url)
        content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)
        
        # Remove imagens ![alt](url)
        content = re.sub(r'!\[([^\]]*)\]\([^\)]+\)', r'\1', content)
        
        # Remove código inline `code`
        content = re.sub(r'`([^`]+)`', r'\1', content)
        
        # Remove blocos de código
        content = re.sub(r'```[\s\S]*?```', '', content)
        
        # Remove headers markdown (mantém o texto)
        content = re.sub(r'^#{1,6}\s+', '', content, flags=re.MULTILINE)
        
        # Remove formatação bold/italic
        content = re.sub(r'\*\*([^\*]+)\*\*', r'\1', content)
        content = re.sub(r'\*([^\*]+)\*', r'\1', content)
        content = re.sub(r'__([^_]+)__', r'\1', content)
        content = re.sub(r'_([^_]+)_', r'\1', content)
        
        # Remove listas (mantém o texto)
        content = re.sub(r'^\s*[-\*\+]\s+', '', content, flags=re.MULTILINE)
        content = re.sub(r'^\s*\d+\.\s+', '', content, flags=re.MULTILINE)
        
        # Remove linhas vazias excessivas
        content = re.sub(r'\n\s*\n', '\n\n', content)
        
        return content.strip()
    
    def _extract_title(self, metadata: Dict, content: str, file_path: Path) -> str:
        """Extrai título do documento"""
        # Prioridade: frontmatter > primeiro heading > nome do arquivo
        if 'title' in metadata:
            return metadata['title']
        
        # Procura primeiro heading
        lines = content.split('\n')
        for line in lines:
            if line.strip().startswith('#'):
                return re.sub(r'^#+\s*', '', line.strip())
        
        # Usa nome do arquivo
        return file_path.stem
    
    def _extract_tags(self, metadata: Dict, content: str) -> List[str]:
        """Extrai tags do documento"""
        tags = []
        
        # Tags do frontmatter
        if 'tags' in metadata:
            if isinstance(metadata['tags'], list):
                tags.extend(metadata['tags'])
            elif isinstance(metadata['tags'], str):
                tags.append(metadata['tags'])
        
        # Tags inline #tag
        inline_tags = re.findall(r'#(\w+)', content)
        tags.extend(inline_tags)
        
        return list(set(tags))  # Remove duplicatas
    
    def chunk_document(self, document: Dict) -> List[Dict]:
        """
        Divide documento em chunks para indexação
        
        Args:
            document: Documento processado
            
        Returns:
            Lista de chunks com metadados
        """
        content = document['content']
        chunks = []
        
        if len(content) <= self.chunk_size:
            # Documento pequeno, um chunk apenas
            chunks.append({
                'content': content,
                'chunk_index': 0,
                'chunk_count': 1,
                'document_metadata': document
            })
        else:
            # Divide em chunks com overlap
            start = 0
            chunk_index = 0
            
            while start < len(content):
                end = start + self.chunk_size
                
                # Se não é o último chunk, tenta quebrar em uma frase
                if end < len(content):
                    # Procura por quebra de parágrafo ou frase
                    last_paragraph = content.rfind('\n\n', start, end)
                    last_sentence = content.rfind('.', start, end)
                    
                    if last_paragraph > start:
                        end = last_paragraph
                    elif last_sentence > start:
                        end = last_sentence + 1
                
                chunk_content = content[start:end].strip()
                
                if chunk_content:
                    chunks.append({
                        'content': chunk_content,
                        'chunk_index': chunk_index,
                        'chunk_count': 0,  # Será atualizado depois
                        'document_metadata': document
                    })
                    chunk_index += 1
                
                # Move para próximo chunk com overlap
                start = max(start + self.chunk_size - self.chunk_overlap, end)
            
            # Atualiza chunk_count em todos os chunks
            for chunk in chunks:
                chunk['chunk_count'] = len(chunks)
        
        return chunks
    
    def get_content_preview(self, content: str, max_length: int = 200) -> str:
        """Gera preview do conteúdo"""
        if len(content) <= max_length:
            return content
        
        # Tenta quebrar em uma frase completa
        preview = content[:max_length]
        last_sentence = preview.rfind('.')
        
        if last_sentence > max_length * 0.7:  # Se a frase não é muito curta
            return preview[:last_sentence + 1]
        
        return preview + "..."

# Instância global do processador
document_processor = DocumentProcessor()
