#!/usr/bin/env python3
"""
Instalação rápida do Ollama + modelos leves
Para dar ARRANQUE ao projeto!
"""

import os
import sys
import subprocess
import requests
import time
import json
from pathlib import Path

def print_header():
    """Header motivacional"""
    print("🚀 INSTALANDO LLM LOCAL PARA DAR ARRANQUE!")
    print("=" * 50)
    print("Vamos turbinar seu projeto com IA local!")
    print()

def baixar_ollama():
    """Baixa Ollama automaticamente"""
    print("📥 Baixando Ollama...")
    
    try:
        # URL direta do Ollama para Windows
        url = "https://ollama.com/download/OllamaSetup.exe"
        
        print(f"🌐 Baixando de: {url}")
        
        # Baixa o arquivo
        response = requests.get(url, stream=True)
        
        if response.status_code == 200:
            with open("OllamaSetup.exe", "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print("✅ Ollama baixado com sucesso!")
            print("🔧 Execute OllamaSetup.exe para instalar")
            return True
        else:
            print(f"❌ Erro no download: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def verificar_ollama():
    """Verifica se Ollama está funcionando"""
    try:
        result = subprocess.run(['ollama', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Ollama detectado!")
            return True
        return False
    except:
        return False

def iniciar_ollama():
    """Inicia serviço Ollama"""
    print("🔄 Iniciando Ollama...")
    
    try:
        # Inicia em background
        subprocess.Popen(['ollama', 'serve'], 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.DEVNULL)
        
        # Aguarda inicialização
        time.sleep(5)
        
        # Testa se está rodando
        response = requests.get('http://localhost:11434/api/tags', timeout=5)
        if response.status_code == 200:
            print("✅ Ollama rodando!")
            return True
        
        print("⚠️ Ollama pode não ter iniciado")
        return False
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def instalar_modelos_rapidos():
    """Instala modelos pequenos e RÁPIDOS"""
    print("⚡ Instalando modelos RÁPIDOS...")
    
    # Modelos em ordem de velocidade (mais rápido primeiro)
    modelos = [
        ("tinyllama", "TinyLlama - Ultra rápido (1.1B)"),
        ("qwen2:0.5b", "Qwen2 0.5B - Muito rápido"),
        ("phi3:mini", "Phi-3 Mini - Rápido e eficiente"),
        ("llama3.2:1b", "Llama 3.2 1B - Pequeno e bom")
    ]
    
    modelo_instalado = None
    
    for modelo, descricao in modelos:
        print(f"\n🔄 Instalando {modelo}...")
        print(f"   {descricao}")
        
        try:
            # Comando para instalar
            result = subprocess.run(['ollama', 'pull', modelo], 
                                  capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print(f"✅ {modelo} instalado!")
                modelo_instalado = modelo
                break  # Para no primeiro que funcionar
            else:
                print(f"❌ Falha: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ Timeout - tentando próximo...")
        except Exception as e:
            print(f"❌ Erro: {e}")
    
    return modelo_instalado

def testar_modelo(modelo):
    """Testa modelo instalado"""
    print(f"\n🧪 Testando {modelo}...")
    
    try:
        # Teste simples
        result = subprocess.run([
            'ollama', 'run', modelo, 
            'Responda em português: O que é direito?'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            resposta = result.stdout.strip()
            print("✅ Teste OK!")
            print(f"🤖 Resposta: {resposta[:100]}...")
            return True
        else:
            print(f"❌ Erro: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def criar_config_rapida(modelo):
    """Cria configuração otimizada"""
    config = {
        "llm_local": {
            "enabled": True,
            "provider": "ollama",
            "model": modelo,
            "base_url": "http://localhost:11434",
            "temperature": 0.3,
            "max_tokens": 1000,
            "stream": True
        },
        "modo_turbo": {
            "enabled": True,
            "usar_local_para": [
                "analise_rapida",
                "extracao_topicos", 
                "categorização",
                "resumos_simples",
                "pre_processamento"
            ],
            "usar_gemini_para": [
                "analise_juridica",
                "conexoes_complexas",
                "criacao_conteudo",
                "respostas_finais"
            ]
        },
        "otimizacoes": {
            "cache_respostas": True,
            "processamento_paralelo": True,
            "timeout_local": 10,
            "timeout_gemini": 30
        }
    }
    
    with open("config_turbo.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ Configuração turbo criada!")

def instalar_dependencias():
    """Instala dependências Python"""
    print("📦 Instalando dependências...")
    
    packages = ["ollama", "httpx", "aiohttp"]
    
    for package in packages:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                          check=True, capture_output=True)
            print(f"✅ {package}")
        except:
            print(f"⚠️ {package} - erro")

def main():
    """Instalação completa"""
    print_header()
    
    # 1. Verifica se já tem Ollama
    if verificar_ollama():
        print("✅ Ollama já instalado!")
    else:
        print("📥 Ollama não encontrado. Baixando...")
        if baixar_ollama():
            print("\n🔧 INSTRUÇÕES:")
            print("1. Execute OllamaSetup.exe")
            print("2. Siga a instalação")
            print("3. Reinicie este script")
            input("\n⏸️ Pressione Enter após instalar...")
            
            if not verificar_ollama():
                print("❌ Ollama não detectado. Instale manualmente.")
                return False
    
    # 2. Instala dependências
    instalar_dependencias()
    
    # 3. Inicia Ollama
    if not iniciar_ollama():
        print("⚠️ Problemas com Ollama. Continuando...")
    
    # 4. Instala modelo rápido
    modelo = instalar_modelos_rapidos()
    if not modelo:
        print("❌ Nenhum modelo instalado!")
        return False
    
    # 5. Testa modelo
    if testar_modelo(modelo):
        print("✅ Modelo funcionando!")
    
    # 6. Cria configuração
    criar_config_rapida(modelo)
    
    # 7. Sucesso!
    print("\n" + "🎉" * 20)
    print("🚀 LLM LOCAL INSTALADO COM SUCESSO!")
    print("🎉" * 20)
    print(f"✅ Modelo: {modelo}")
    print("✅ Configuração: config_turbo.json")
    print("✅ Sistema: Pronto para ARRANQUE!")
    print()
    print("💡 PRÓXIMOS PASSOS:")
    print("   1. Execute: python sistema_turbo.py")
    print("   2. Veja a diferença de velocidade!")
    print("   3. Configure seus cofres")
    print()
    print("🔥 SEU PROJETO AGORA TEM ARRANQUE!")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Instalação cancelada")
    except Exception as e:
        print(f"\n❌ Erro: {e}")
