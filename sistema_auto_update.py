#!/usr/bin/env python3
"""
Sistema de Atualização Automática para Obsidian
Monitora e atualiza notas automaticamente
"""

import asyncio
import os
import time
import json
from pathlib import Path
from typing import Dict, List, Set
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import threading

from obsidian_multi_agent_system import ObsidianMultiAgentSystem

class MonitorObsidian(FileSystemEventHandler):
    """Monitor que detecta mudanças nos cofres"""
    
    def __init__(self, sistema_auto):
        self.sistema_auto = sistema_auto
        self.last_update = time.time()
        self.pending_updates = set()
        
    def on_modified(self, event):
        """Quando arquivo é modificado"""
        if not event.is_directory and event.src_path.endswith('.md'):
            print(f"📝 Arquivo modificado: {event.src_path}")
            self.pending_updates.add(event.src_path)
            self.schedule_update()
    
    def on_created(self, event):
        """Quando arquivo é criado"""
        if not event.is_directory and event.src_path.endswith('.md'):
            print(f"✨ Novo arquivo: {event.src_path}")
            self.pending_updates.add(event.src_path)
            self.schedule_update()
    
    def on_deleted(self, event):
        """Quando arquivo é deletado"""
        if not event.is_directory and event.src_path.endswith('.md'):
            print(f"🗑️ Arquivo removido: {event.src_path}")
            self.pending_updates.add(event.src_path)
            self.schedule_update()
    
    def schedule_update(self):
        """Agenda atualização (evita spam)"""
        self.last_update = time.time()
        # Agenda para 3 segundos depois (evita múltiplas atualizações)

class SistemaAutoUpdate:
    """Sistema principal de atualização automática"""
    
    def __init__(self):
        self.sistema_obsidian = ObsidianMultiAgentSystem()
        self.observer = Observer()
        self.monitor = MonitorObsidian(self)
        self.cofres_monitorados = {}
        self.running = False
        self.update_interval = 5  # segundos
        self.last_check = {}
        
        print("🔄 Sistema de Atualização Automática inicializado!")
    
    def adicionar_cofre_monitorado(self, nome: str, caminho: str, descricao: str):
        """Adiciona cofre para monitoramento automático"""
        path = Path(caminho)
        
        if not path.exists():
            print(f"❌ Caminho não encontrado: {caminho}")
            return False
        
        # Adiciona ao sistema Obsidian
        sucesso = self.sistema_obsidian.add_vault(nome, caminho, descricao)
        
        if sucesso:
            # Adiciona ao monitoramento
            self.cofres_monitorados[nome] = {
                'caminho': caminho,
                'descricao': descricao,
                'last_update': time.time(),
                'files_count': len(list(path.rglob("*.md")))
            }
            
            # Inicia monitoramento da pasta
            self.observer.schedule(self.monitor, caminho, recursive=True)
            
            print(f"✅ Cofre '{nome}' adicionado ao monitoramento automático")
            print(f"📁 Monitorando: {caminho}")
            
            return True
        
        return False
    
    async def verificar_mudancas_periodicas(self):
        """Verifica mudanças periodicamente"""
        while self.running:
            try:
                await self.verificar_todos_cofres()
                await asyncio.sleep(self.update_interval)
            except Exception as e:
                print(f"❌ Erro na verificação periódica: {e}")
                await asyncio.sleep(self.update_interval)
    
    async def verificar_todos_cofres(self):
        """Verifica todos os cofres por mudanças"""
        mudancas_detectadas = False
        
        for nome, info in self.cofres_monitorados.items():
            caminho = Path(info['caminho'])
            
            if not caminho.exists():
                print(f"⚠️ Cofre '{nome}' não encontrado: {caminho}")
                continue
            
            # Conta arquivos atuais
            arquivos_atuais = list(caminho.rglob("*.md"))
            count_atual = len(arquivos_atuais)
            count_anterior = info['files_count']
            
            # Verifica se houve mudanças
            if count_atual != count_anterior:
                print(f"🔄 Mudanças detectadas em '{nome}': {count_anterior} → {count_atual} arquivos")
                
                # Atualiza contagem
                self.cofres_monitorados[nome]['files_count'] = count_atual
                self.cofres_monitorados[nome]['last_update'] = time.time()
                
                mudancas_detectadas = True
            
            # Verifica modificações por timestamp
            for arquivo in arquivos_atuais:
                mod_time = arquivo.stat().st_mtime
                if mod_time > info['last_update']:
                    print(f"📝 Arquivo modificado: {arquivo.name}")
                    mudancas_detectadas = True
        
        # Se houve mudanças, reindexar
        if mudancas_detectadas:
            await self.reindexar_automatico()
    
    async def reindexar_automatico(self):
        """Reindexação automática inteligente"""
        print("🔄 Iniciando reindexação automática...")
        
        try:
            # Reindexar todos os cofres
            await self.sistema_obsidian.index_all_vaults()
            
            # Atualizar timestamps
            for nome in self.cofres_monitorados:
                self.cofres_monitorados[nome]['last_update'] = time.time()
            
            print("✅ Reindexação automática concluída!")
            
            # Salvar estado
            self.salvar_estado()
            
        except Exception as e:
            print(f"❌ Erro na reindexação: {e}")
    
    def iniciar_monitoramento(self):
        """Inicia o monitoramento automático"""
        if not self.running:
            self.running = True
            self.observer.start()
            print("🔄 Monitoramento automático INICIADO!")
            print(f"📊 Monitorando {len(self.cofres_monitorados)} cofres")
            print(f"⏱️ Verificação a cada {self.update_interval} segundos")
            
            # Inicia verificação periódica em background
            asyncio.create_task(self.verificar_mudancas_periodicas())
        else:
            print("⚠️ Monitoramento já está ativo")
    
    def parar_monitoramento(self):
        """Para o monitoramento"""
        if self.running:
            self.running = False
            self.observer.stop()
            self.observer.join()
            print("⏹️ Monitoramento automático PARADO!")
        else:
            print("⚠️ Monitoramento já estava parado")
    
    def salvar_estado(self):
        """Salva estado do monitoramento"""
        estado = {
            'cofres_monitorados': self.cofres_monitorados,
            'last_save': time.time(),
            'update_interval': self.update_interval
        }
        
        with open('estado_auto_update.json', 'w', encoding='utf-8') as f:
            json.dump(estado, f, indent=2, ensure_ascii=False)
    
    def carregar_estado(self):
        """Carrega estado salvo"""
        try:
            with open('estado_auto_update.json', 'r', encoding='utf-8') as f:
                estado = json.load(f)
            
            self.cofres_monitorados = estado.get('cofres_monitorados', {})
            self.update_interval = estado.get('update_interval', 5)
            
            print(f"✅ Estado carregado: {len(self.cofres_monitorados)} cofres")
            
        except FileNotFoundError:
            print("📝 Nenhum estado anterior encontrado")
        except Exception as e:
            print(f"❌ Erro ao carregar estado: {e}")
    
    async def chat_com_auto_update(self, pergunta: str) -> str:
        """Chat que sempre usa dados atualizados"""
        # Verifica se precisa atualizar antes de responder
        await self.verificar_todos_cofres()
        
        # Usa o sistema Obsidian normal
        return await self.sistema_obsidian.chat(pergunta)
    
    def status_monitoramento(self) -> Dict:
        """Retorna status do monitoramento"""
        return {
            'ativo': self.running,
            'cofres_monitorados': len(self.cofres_monitorados),
            'intervalo_verificacao': self.update_interval,
            'cofres': {
                nome: {
                    'arquivos': info['files_count'],
                    'ultima_atualizacao': datetime.fromtimestamp(info['last_update']).strftime('%H:%M:%S')
                }
                for nome, info in self.cofres_monitorados.items()
            }
        }

async def demo_auto_update():
    """Demonstração do sistema de auto-update"""
    print("🔄 DEMO SISTEMA AUTO-UPDATE")
    print("=" * 35)
    
    # Inicializa sistema
    sistema = SistemaAutoUpdate()
    
    # Carrega estado anterior
    sistema.carregar_estado()
    
    # Adiciona cofres para monitoramento
    cofres_para_monitorar = [
        ("Direito Penal", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Direito Penal", "Direito penal e processo penal"),
        ("Constitucional", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Constitucional", "Direito constitucional"),
        ("Consumidor", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Consumidor", "Direito do consumidor")
    ]
    
    print("\n📁 Adicionando cofres ao monitoramento...")
    for nome, caminho, desc in cofres_para_monitorar:
        sistema.adicionar_cofre_monitorado(nome, caminho, desc)
    
    # Inicia monitoramento
    sistema.iniciar_monitoramento()
    
    # Indexação inicial
    print("\n🔄 Indexação inicial...")
    await sistema.sistema_obsidian.index_all_vaults()
    
    # Status
    status = sistema.status_monitoramento()
    print(f"\n📊 STATUS:")
    print(f"   • Monitoramento: {'✅ Ativo' if status['ativo'] else '❌ Inativo'}")
    print(f"   • Cofres: {status['cofres_monitorados']}")
    print(f"   • Intervalo: {status['intervalo_verificacao']}s")
    
    # Teste de chat
    print(f"\n💬 Testando chat com auto-update...")
    resposta = await sistema.chat_com_auto_update("Quantos cofres estão sendo monitorados?")
    print(f"🤖 {resposta[:200]}...")
    
    print(f"\n✅ Sistema de auto-update funcionando!")
    print(f"💡 Agora suas notas serão atualizadas automaticamente!")
    
    return sistema

async def main():
    """Menu principal"""
    print("🔄 SISTEMA AUTO-UPDATE OBSIDIAN")
    print("=" * 35)
    print("Atualização automática de notas!")
    print()
    
    print("Opções:")
    print("1. 🧪 Demo auto-update")
    print("2. 🔄 Iniciar monitoramento")
    print("3. 📊 Ver status")
    
    try:
        choice = input("\nEscolha: ").strip()
        
        if choice == "1":
            sistema = await demo_auto_update()
            
            print("\n⏸️ Pressione Enter para parar monitoramento...")
            input()
            sistema.parar_monitoramento()
            
        elif choice == "2":
            sistema = SistemaAutoUpdate()
            sistema.carregar_estado()
            sistema.iniciar_monitoramento()
            
            print("✅ Monitoramento iniciado!")
            print("⏸️ Pressione Ctrl+C para parar...")
            
            try:
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                sistema.parar_monitoramento()
                
        elif choice == "3":
            sistema = SistemaAutoUpdate()
            sistema.carregar_estado()
            status = sistema.status_monitoramento()
            
            print(f"\n📊 STATUS MONITORAMENTO:")
            print(f"   • Ativo: {status['ativo']}")
            print(f"   • Cofres: {status['cofres_monitorados']}")
            for nome, info in status['cofres'].items():
                print(f"     - {nome}: {info['arquivos']} arquivos (última: {info['ultima_atualizacao']})")
        else:
            print("❌ Opção inválida")
            
    except KeyboardInterrupt:
        print("\n👋 Sistema encerrado")

if __name__ == "__main__":
    asyncio.run(main())
