#!/usr/bin/env python3
"""
Teste direto da API Gemini
"""

import google.generativeai as genai
from dotenv import load_dotenv
import os

def test_gemini_direct():
    """Testa a API Gemini diretamente"""

    # Testa com a API Key fornecida diretamente
    api_key = "AIzaSyDASqL2UXOSEuXX0UTTOXzYciyoUwuk1mc"

    print(f"🔑 API Key carregada: {api_key[:20]}...{api_key[-10:] if api_key else 'NENHUMA'}")

    if not api_key:
        print("❌ API Key não encontrada no arquivo .env")
        return False

    try:
        # Configura a API
        genai.configure(api_key=api_key)

        # Cria o modelo (testando diferentes versões)
        try:
            model = genai.GenerativeModel('gemini-1.5-flash')
        except:
            try:
                model = genai.GenerativeModel('gemini-1.0-pro')
            except:
                model = genai.GenerativeModel('models/gemini-1.5-flash-latest')

        # Testa uma pergunta simples
        print("🧪 Testando pergunta simples...")
        response = model.generate_content("Olá! Você pode me dizer que dia é hoje?")

        print(f"✅ Resposta do Gemini: {response.text}")
        return True

    except Exception as e:
        print(f"❌ Erro ao testar Gemini: {e}")
        return False

if __name__ == "__main__":
    print("🧪 TESTE DIRETO DA API GEMINI")
    print("=" * 40)

    if test_gemini_direct():
        print("\n🎉 API Gemini funcionando!")
        print("💡 O problema pode estar na configuração do sistema.")
    else:
        print("\n❌ API Gemini não está funcionando.")
        print("💡 Verifique se a API Key está correta e ativa.")
