#!/usr/bin/env python3
"""
Script para testar o chat com API Key real
"""

import requests
import json

def test_indexing():
    """Testa a indexação das notas"""
    print("🔄 Testando indexação...")
    
    url = "http://localhost:8000/api/index"
    data = {"force_reindex": False}
    
    try:
        response = requests.post(url, json=data, timeout=60)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Indexação concluída: {result}")
            return True
        else:
            print(f"❌ Erro na indexação: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro na indexação: {e}")
        return False

def test_chat():
    """Testa o chat com uma pergunta simples"""
    print("💬 Testando chat...")
    
    url = "http://localhost:8000/api/chat"
    data = {
        "message": "Olá! Você pode me explicar o que são os princípios fundamentais do Direito Constitucional?",
        "session_id": "test_session_123"
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Chat funcionando!")
            print(f"📝 Resposta: {result['response'][:200]}...")
            print(f"📚 Fontes: {len(result['sources'])} documentos encontrados")
            return True
        else:
            print(f"❌ Erro no chat: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro no chat: {e}")
        return False

def test_health():
    """Testa se a API está funcionando"""
    print("🏥 Testando saúde da API...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ API funcionando: {result['status']}")
            return True
        else:
            print(f"❌ API com problemas: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API não acessível: {e}")
        return False

def main():
    print("🧪 TESTANDO CHAT INTELIGENTE OBSIDIAN")
    print("=" * 50)
    
    # Testa saúde da API
    if not test_health():
        print("❌ API não está funcionando. Verifique se o servidor está rodando.")
        return
    
    # Testa indexação
    if not test_indexing():
        print("⚠️ Indexação falhou, mas vamos tentar o chat mesmo assim...")
    
    # Testa chat
    if test_chat():
        print("\n🎉 SUCESSO! Seu chat está funcionando com a API Key real!")
        print("🌐 Acesse: http://localhost:8000")
        print("💡 Agora você pode conversar com suas notas!")
    else:
        print("\n❌ Chat não está funcionando. Verifique a API Key do Gemini.")

if __name__ == "__main__":
    main()
