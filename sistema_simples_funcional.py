#!/usr/bin/env python3
"""
Sistema SIMPLES e FUNCIONAL para Obsidian
Sem complicações - só o que funciona
"""

import os
import asyncio
import re
from pathlib import Path
from typing import List, Dict
import google.generativeai as genai

# Configuração simples
GOOGLE_API_KEY = "AIzaSyCq1rBFLFqX04jQ2ejd5Tkbane-lTvIsVU"
genai.configure(api_key=GOOGLE_API_KEY)

class SistemaSimples:
    """Sistema simples que funciona"""
    
    def __init__(self):
        self.model = genai.GenerativeModel('gemini-1.5-pro')
        self.cofres = {}
        self.arquivos_carregados = {}
        
        print("✅ Sistema Simples inicializado!")
        print("🔥 Gemini configurado e funcionando!")
    
    def adicionar_cofre(self, nome: str, caminho: str) -> bool:
        """Adiciona cofre de forma simples"""
        path = Path(caminho)
        
        if not path.exists():
            print(f"❌ Caminho não existe: {caminho}")
            return False
        
        # Busca arquivos .md
        arquivos_md = list(path.rglob("*.md"))
        
        if not arquivos_md:
            print(f"❌ Nenhum arquivo .md encontrado em: {caminho}")
            return False
        
        self.cofres[nome] = {
            'caminho': caminho,
            'arquivos': len(arquivos_md)
        }
        
        print(f"✅ Cofre '{nome}' adicionado!")
        print(f"📁 {len(arquivos_md)} arquivos .md encontrados")
        
        return True
    
    def carregar_arquivos(self, nome_cofre: str) -> bool:
        """Carrega arquivos de um cofre"""
        if nome_cofre not in self.cofres:
            print(f"❌ Cofre '{nome_cofre}' não encontrado")
            return False
        
        caminho = self.cofres[nome_cofre]['caminho']
        path = Path(caminho)
        
        arquivos_md = list(path.rglob("*.md"))
        conteudo_total = []
        
        print(f"📖 Carregando arquivos de '{nome_cofre}'...")
        
        for arquivo in arquivos_md[:10]:  # Máximo 10 arquivos para não sobrecarregar
            try:
                with open(arquivo, 'r', encoding='utf-8') as f:
                    conteudo = f.read()
                    conteudo_total.append(f"=== {arquivo.name} ===\n{conteudo}\n")
            except Exception as e:
                print(f"⚠️ Erro ao ler {arquivo.name}: {e}")
        
        self.arquivos_carregados[nome_cofre] = "\n".join(conteudo_total)
        
        print(f"✅ {len(conteudo_total)} arquivos carregados de '{nome_cofre}'")
        return True
    
    def extrair_info_basica(self, texto: str) -> Dict:
        """Extrai informações básicas do texto"""
        if not texto:
            return {}
        
        # Análise simples
        palavras_juridicas = ['direito', 'lei', 'artigo', 'crime', 'processo', 'tribunal', 'juiz']
        topicos_encontrados = [palavra for palavra in palavras_juridicas if palavra in texto.lower()]
        
        artigos = re.findall(r'art\.?\s*(\d+)', texto.lower())
        
        return {
            'palavras': len(texto.split()),
            'linhas': len(texto.split('\n')),
            'topicos_juridicos': topicos_encontrados[:5],
            'artigos_mencionados': [f"art. {art}" for art in artigos[:5]],
            'tem_conteudo_juridico': len(topicos_encontrados) > 0
        }
    
    async def chat_simples(self, pergunta: str) -> str:
        """Chat simples que funciona"""
        print(f"💬 Pergunta: {pergunta}")
        
        # Prepara contexto
        contexto = ""
        info_geral = {}
        
        if self.arquivos_carregados:
            print("📊 Analisando arquivos carregados...")
            
            for nome_cofre, conteudo in self.arquivos_carregados.items():
                info = self.extrair_info_basica(conteudo)
                info_geral[nome_cofre] = info
                
                # Pega uma amostra do conteúdo
                amostra = conteudo[:1000] + "..." if len(conteudo) > 1000 else conteudo
                contexto += f"\n=== COFRE: {nome_cofre} ===\n{amostra}\n"
        
        # Monta prompt
        prompt = f"""
Você é um assistente especializado em direito brasileiro.

CONTEXTO DOS COFRES:
{contexto}

ANÁLISE PRÉVIA:
{info_geral}

PERGUNTA DO USUÁRIO: {pergunta}

Responda de forma clara e objetiva em português, usando o contexto fornecido quando relevante.
"""
        
        try:
            print("🤖 Gerando resposta...")
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            
            resposta = response.text
            
            print("✅ Resposta gerada!")
            return resposta
            
        except Exception as e:
            return f"❌ Erro ao gerar resposta: {e}"
    
    def listar_cofres(self):
        """Lista cofres configurados"""
        if not self.cofres:
            print("📁 Nenhum cofre configurado")
            return
        
        print("📁 COFRES CONFIGURADOS:")
        for nome, info in self.cofres.items():
            carregado = "✅" if nome in self.arquivos_carregados else "⏸️"
            print(f"   {carregado} {nome}: {info['arquivos']} arquivos")
    
    def status(self):
        """Mostra status do sistema"""
        print("\n📊 STATUS DO SISTEMA:")
        print(f"   • Cofres configurados: {len(self.cofres)}")
        print(f"   • Cofres carregados: {len(self.arquivos_carregados)}")
        print(f"   • Gemini: ✅ Funcionando")

async def demo_simples():
    """Demo do sistema simples"""
    print("🎯 DEMO SISTEMA SIMPLES")
    print("=" * 30)
    
    # Inicializa
    sistema = SistemaSimples()
    
    # Tenta adicionar cofres
    cofres_teste = [
        ("Direito Penal", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Direito Penal"),
        ("Constitucional", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Constitucional"),
        ("Consumidor", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Consumidor")
    ]
    
    cofres_adicionados = []
    
    for nome, caminho in cofres_teste:
        if sistema.adicionar_cofre(nome, caminho):
            cofres_adicionados.append(nome)
    
    if not cofres_adicionados:
        print("⚠️ Nenhum cofre foi adicionado. Testando sem cofres...")
    else:
        # Carrega arquivos do primeiro cofre
        primeiro_cofre = cofres_adicionados[0]
        sistema.carregar_arquivos(primeiro_cofre)
    
    # Status
    sistema.status()
    
    # Teste de chat
    perguntas = [
        "O que você pode fazer?",
        "Explique o que é direito penal",
        "Quais são os principais crimes?"
    ]
    
    for pergunta in perguntas:
        print(f"\n{'='*50}")
        resposta = await sistema.chat_simples(pergunta)
        print(f"🤖 {resposta[:200]}...")
        print("\n⏸️ Pressione Enter para continuar...")
        input()

async def menu_interativo():
    """Menu interativo simples"""
    sistema = SistemaSimples()
    
    while True:
        print("\n" + "="*40)
        print("🔧 SISTEMA SIMPLES OBSIDIAN")
        print("="*40)
        print("1. 📁 Adicionar cofre")
        print("2. 📖 Carregar arquivos")
        print("3. 💬 Chat")
        print("4. 📊 Status")
        print("5. 🚪 Sair")
        
        try:
            opcao = input("\nEscolha (1-5): ").strip()
            
            if opcao == "1":
                nome = input("Nome do cofre: ").strip()
                caminho = input("Caminho do cofre: ").strip()
                sistema.adicionar_cofre(nome, caminho)
                
            elif opcao == "2":
                sistema.listar_cofres()
                if sistema.cofres:
                    nome = input("Nome do cofre para carregar: ").strip()
                    sistema.carregar_arquivos(nome)
                
            elif opcao == "3":
                pergunta = input("Sua pergunta: ").strip()
                if pergunta:
                    resposta = await sistema.chat_simples(pergunta)
                    print(f"\n🤖 RESPOSTA:\n{resposta}")
                
            elif opcao == "4":
                sistema.status()
                sistema.listar_cofres()
                
            elif opcao == "5":
                print("👋 Até logo!")
                break
                
            else:
                print("❌ Opção inválida")
                
        except KeyboardInterrupt:
            print("\n👋 Sistema encerrado")
            break
        except Exception as e:
            print(f"❌ Erro: {e}")

async def main():
    """Menu principal"""
    print("🔧 SISTEMA SIMPLES E FUNCIONAL")
    print("=" * 35)
    print("Versão que realmente funciona!")
    print()
    
    print("Escolha:")
    print("1. 🧪 Demo automática")
    print("2. 💬 Menu interativo")
    
    try:
        choice = input("\nOpção (1-2): ").strip()
        
        if choice == "1":
            await demo_simples()
        elif choice == "2":
            await menu_interativo()
        else:
            print("❌ Opção inválida")
            
    except KeyboardInterrupt:
        print("\n👋 Sistema encerrado")

if __name__ == "__main__":
    asyncio.run(main())
