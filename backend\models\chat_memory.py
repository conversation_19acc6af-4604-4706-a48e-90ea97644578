from typing import List, Dict, Optional
from datetime import datetime
import json

class ChatMemory:
    """Gerenciador de memória do chat"""
    
    def __init__(self, max_messages: int = 50):
        self.max_messages = max_messages
        self.conversations = {}
    
    def add_message(self, session_id: str, role: str, content: str, metadata: Dict = None):
        """Adiciona mensagem à memória"""
        if session_id not in self.conversations:
            self.conversations[session_id] = []
        
        message = {
            'role': role,
            'content': content,
            'metadata': metadata or {},
            'timestamp': datetime.now().isoformat()
        }
        
        self.conversations[session_id].append(message)
        
        # Limita o número de mensagens
        if len(self.conversations[session_id]) > self.max_messages:
            self.conversations[session_id] = self.conversations[session_id][-self.max_messages:]
    
    def get_conversation(self, session_id: str) -> List[Dict]:
        """Obtém conversa completa"""
        return self.conversations.get(session_id, [])
    
    def clear_conversation(self, session_id: str):
        """Limpa conversa"""
        if session_id in self.conversations:
            del self.conversations[session_id]
