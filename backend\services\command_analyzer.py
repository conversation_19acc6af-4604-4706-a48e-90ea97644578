import re
from typing import Dict, Optional, List, Tuple
from enum import Enum

class CommandType(Enum):
    """Tipos de comandos disponíveis"""
    CHAT = "chat"                    # Conversa normal
    MODIFY_NOTE = "modify_note"      # Modificar nota existente
    CREATE_NOTE = "create_note"      # Criar nova nota
    ADD_TO_NOTE = "add_to_note"      # Adicionar conteúdo à nota
    SEARCH_NOTES = "search_notes"    # Buscar nas notas
    LIST_NOTES = "list_notes"        # Listar notas

class CommandAnalyzer:
    """Analisa mensagens do usuário para detectar comandos de edição"""
    
    def __init__(self):
        # Padrões para detectar comandos de modificação
        self.modify_patterns = [
            r"modifi(?:que|car|ca)\s+(?:a\s+)?nota\s+[\"']?([^\"']+)[\"']?",
            r"edit(?:e|ar|a)\s+(?:a\s+)?nota\s+[\"']?([^\"']+)[\"']?",
            r"alter(?:e|ar|a)\s+(?:a\s+)?nota\s+[\"']?([^\"']+)[\"']?",
            r"atualiz(?:e|ar|a)\s+(?:a\s+)?nota\s+[\"']?([^\"']+)[\"']?",
            r"corrig(?:e|ir|a)\s+(?:a\s+)?nota\s+[\"']?([^\"']+)[\"']?",
        ]
        
        # Padrões para detectar comandos de criação
        self.create_patterns = [
            r"cri(?:e|ar|a)\s+(?:uma\s+)?(?:nova\s+)?nota\s+(?:sobre\s+|chamada\s+)?[\"']?([^\"']+)[\"']?",
            r"(?:nova|new)\s+nota\s+[\"']?([^\"']+)[\"']?",
            r"escreva\s+(?:uma\s+)?nota\s+(?:sobre\s+)?[\"']?([^\"']+)[\"']?",
            r"faça\s+(?:uma\s+)?nota\s+(?:sobre\s+)?[\"']?([^\"']+)[\"']?",
        ]
        
        # Padrões para detectar comandos de adição
        self.add_patterns = [
            r"adicion(?:e|ar|a)\s+(?:.*?\s+)?(?:à|na|para)\s+nota\s+[\"']?([^\"']+)[\"']?",
            r"inclua\s+(?:.*?\s+)?(?:à|na|para)\s+nota\s+[\"']?([^\"']+)[\"']?",
            r"acrescente\s+(?:.*?\s+)?(?:à|na|para)\s+nota\s+[\"']?([^\"']+)[\"']?",
            r"expanda\s+(?:a\s+)?nota\s+[\"']?([^\"']+)[\"']?",
        ]
        
        # Palavras-chave para tipos de modificação
        self.modification_keywords = {
            'adicionar': ['adicionar', 'incluir', 'acrescentar', 'inserir'],
            'atualizar': ['atualizar', 'modificar', 'alterar', 'mudar'],
            'corrigir': ['corrigir', 'consertar', 'arrumar', 'ajustar'],
            'expandir': ['expandir', 'ampliar', 'detalhar', 'elaborar'],
            'resumir': ['resumir', 'sintetizar', 'condensar'],
            'reorganizar': ['reorganizar', 'reestruturar', 'reordenar']
        }
        
        # Palavras-chave para seções
        self.section_keywords = [
            'introdução', 'conclusão', 'desenvolvimento', 'exemplos',
            'definição', 'conceitos', 'características', 'aplicações',
            'vantagens', 'desvantagens', 'referências', 'bibliografia'
        ]
    
    def analyze_command(self, message: str) -> Dict:
        """
        Analisa uma mensagem para detectar comandos de edição
        
        Args:
            message: Mensagem do usuário
            
        Returns:
            Dict com informações do comando detectado
        """
        message_lower = message.lower().strip()
        
        # Verifica comando de modificação
        modify_result = self._detect_modify_command(message_lower, message)
        if modify_result['detected']:
            return modify_result
        
        # Verifica comando de criação
        create_result = self._detect_create_command(message_lower, message)
        if create_result['detected']:
            return create_result
        
        # Verifica comando de adição
        add_result = self._detect_add_command(message_lower, message)
        if add_result['detected']:
            return add_result
        
        # Se não detectou comando específico, é chat normal
        return {
            'detected': True,
            'command_type': CommandType.CHAT,
            'confidence': 1.0,
            'parameters': {},
            'original_message': message
        }
    
    def _detect_modify_command(self, message_lower: str, original_message: str) -> Dict:
        """Detecta comandos de modificação de nota"""
        for pattern in self.modify_patterns:
            match = re.search(pattern, message_lower)
            if match:
                note_title = match.group(1).strip()
                modification_type = self._detect_modification_type(message_lower)
                
                return {
                    'detected': True,
                    'command_type': CommandType.MODIFY_NOTE,
                    'confidence': 0.9,
                    'parameters': {
                        'note_title': note_title,
                        'modification_type': modification_type,
                        'modification_request': self._extract_modification_request(original_message),
                        'target_section': self._extract_target_section(message_lower)
                    },
                    'original_message': original_message
                }
        
        return {'detected': False}
    
    def _detect_create_command(self, message_lower: str, original_message: str) -> Dict:
        """Detecta comandos de criação de nota"""
        for pattern in self.create_patterns:
            match = re.search(pattern, message_lower)
            if match:
                note_title = match.group(1).strip()
                content_type = self._detect_content_type(message_lower)
                
                return {
                    'detected': True,
                    'command_type': CommandType.CREATE_NOTE,
                    'confidence': 0.9,
                    'parameters': {
                        'note_title': note_title,
                        'content_type': content_type,
                        'content_request': self._extract_content_request(original_message)
                    },
                    'original_message': original_message
                }
        
        return {'detected': False}
    
    def _detect_add_command(self, message_lower: str, original_message: str) -> Dict:
        """Detecta comandos de adição de conteúdo"""
        for pattern in self.add_patterns:
            match = re.search(pattern, message_lower)
            if match:
                note_title = match.group(1).strip()
                target_section = self._extract_target_section(message_lower)
                content_to_add = self._extract_content_to_add(original_message)
                
                return {
                    'detected': True,
                    'command_type': CommandType.ADD_TO_NOTE,
                    'confidence': 0.8,
                    'parameters': {
                        'note_title': note_title,
                        'target_section': target_section or 'final',
                        'content_to_add': content_to_add
                    },
                    'original_message': original_message
                }
        
        return {'detected': False}
    
    def _detect_modification_type(self, message_lower: str) -> str:
        """Detecta o tipo de modificação solicitada"""
        for mod_type, keywords in self.modification_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                return mod_type
        return 'atualizar'  # Padrão
    
    def _detect_content_type(self, message_lower: str) -> str:
        """Detecta o tipo de conteúdo para nova nota"""
        if any(word in message_lower for word in ['resumo', 'síntese']):
            return 'resumo'
        elif any(word in message_lower for word in ['conceito', 'definição']):
            return 'conceitual'
        elif any(word in message_lower for word in ['exemplo', 'prático']):
            return 'prático'
        elif any(word in message_lower for word in ['pesquisa', 'estudo']):
            return 'pesquisa'
        else:
            return 'geral'
    
    def _extract_target_section(self, message_lower: str) -> Optional[str]:
        """Extrai a seção alvo da mensagem"""
        # Procura por padrões como "na seção X", "na parte X", etc.
        section_patterns = [
            r"(?:na|à|para)\s+seção\s+[\"']?([^\"']+)[\"']?",
            r"(?:na|à|para)\s+parte\s+[\"']?([^\"']+)[\"']?",
            r"(?:no|ao)\s+tópico\s+[\"']?([^\"']+)[\"']?",
            r"(?:em|na)\s+([a-záàâãéêíóôõúç]+)(?:\s+da\s+nota)?"
        ]
        
        for pattern in section_patterns:
            match = re.search(pattern, message_lower)
            if match:
                section = match.group(1).strip()
                # Verifica se é uma seção conhecida
                if any(keyword in section for keyword in self.section_keywords):
                    return section
        
        return None
    
    def _extract_modification_request(self, message: str) -> str:
        """Extrai a solicitação de modificação da mensagem"""
        # Remove padrões de comando para focar no conteúdo
        clean_message = message
        for pattern in self.modify_patterns:
            clean_message = re.sub(pattern, '', clean_message, flags=re.IGNORECASE)
        
        return clean_message.strip()
    
    def _extract_content_request(self, message: str) -> str:
        """Extrai a solicitação de conteúdo da mensagem"""
        # Remove padrões de comando para focar no conteúdo
        clean_message = message
        for pattern in self.create_patterns:
            clean_message = re.sub(pattern, '', clean_message, flags=re.IGNORECASE)
        
        return clean_message.strip()
    
    def _extract_content_to_add(self, message: str) -> str:
        """Extrai o conteúdo a ser adicionado da mensagem"""
        # Remove padrões de comando para focar no conteúdo
        clean_message = message
        for pattern in self.add_patterns:
            clean_message = re.sub(pattern, '', clean_message, flags=re.IGNORECASE)
        
        return clean_message.strip()
    
    def get_command_examples(self) -> Dict[str, List[str]]:
        """Retorna exemplos de comandos para cada tipo"""
        return {
            'modificar_nota': [
                "Modifique a nota 'Direito Constitucional' adicionando exemplos práticos",
                "Atualize a nota sobre neurociência com novas descobertas",
                "Corrija a nota 'Marketing Digital' na seção de conceitos"
            ],
            'criar_nota': [
                "Crie uma nova nota sobre 'Inteligência Artificial'",
                "Faça uma nota chamada 'Resumo de Filosofia'",
                "Escreva uma nota sobre os princípios da física quântica"
            ],
            'adicionar_conteudo': [
                "Adicione exemplos à nota 'Programação Python'",
                "Inclua uma seção de conclusão na nota sobre economia",
                "Acrescente referências bibliográficas à nota de história"
            ]
        }

# Instância global do analisador de comandos
command_analyzer = CommandAnalyzer()
