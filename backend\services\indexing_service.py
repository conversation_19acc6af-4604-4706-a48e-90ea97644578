import asyncio
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional
from sentence_transformers import SentenceTransformer
import faiss
import pickle
import json
from datetime import datetime

from ..models.document_processor import document_processor
from ..models.gemini_client import gemini_client
from ..utils.database import db_manager
from ..utils.config import config

class IndexingService:
    """Serviço de indexação de documentos com embeddings vetoriais"""
    
    def __init__(self):
        self.embedding_model = None
        self.vector_index = None
        self.document_chunks = []
        self.index_metadata = {}
        self.vector_store_path = config.VECTOR_STORE_PATH
        self._initialize_embedding_model()
        self._load_or_create_index()
    
    def _initialize_embedding_model(self):
        """Inicializa o modelo de embeddings"""
        try:
            print("Carregando modelo de embeddings...")
            self.embedding_model = SentenceTransformer(config.EMBEDDING_MODEL)
            print("Modelo de embeddings carregado com sucesso!")
        except Exception as e:
            raise Exception(f"Erro ao carregar modelo de embeddings: {e}")
    
    def _load_or_create_index(self):
        """Carrega índice existente ou cria um novo"""
        index_file = self.vector_store_path / "faiss_index.bin"
        chunks_file = self.vector_store_path / "document_chunks.pkl"
        metadata_file = self.vector_store_path / "index_metadata.json"
        
        if all(f.exists() for f in [index_file, chunks_file, metadata_file]):
            try:
                # Carrega índice existente
                self.vector_index = faiss.read_index(str(index_file))
                
                with open(chunks_file, 'rb') as f:
                    self.document_chunks = pickle.load(f)
                
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    self.index_metadata = json.load(f)
                
                print(f"Índice carregado: {len(self.document_chunks)} chunks indexados")
                
            except Exception as e:
                print(f"Erro ao carregar índice: {e}. Criando novo índice...")
                self._create_new_index()
        else:
            self._create_new_index()
    
    def _create_new_index(self):
        """Cria um novo índice FAISS"""
        # Dimensão dos embeddings do modelo
        embedding_dim = self.embedding_model.get_sentence_embedding_dimension()
        
        # Cria índice FAISS (Inner Product para similaridade de cosseno)
        self.vector_index = faiss.IndexFlatIP(embedding_dim)
        self.document_chunks = []
        self.index_metadata = {
            'created_at': datetime.now().isoformat(),
            'embedding_model': config.EMBEDDING_MODEL,
            'embedding_dimension': embedding_dim,
            'total_documents': 0,
            'total_chunks': 0
        }
        
        print("Novo índice FAISS criado")
    
    def _save_index(self):
        """Salva o índice no disco"""
        try:
            self.vector_store_path.mkdir(parents=True, exist_ok=True)
            
            # Salva índice FAISS
            faiss.write_index(self.vector_index, str(self.vector_store_path / "faiss_index.bin"))
            
            # Salva chunks
            with open(self.vector_store_path / "document_chunks.pkl", 'wb') as f:
                pickle.dump(self.document_chunks, f)
            
            # Salva metadados
            self.index_metadata['updated_at'] = datetime.now().isoformat()
            self.index_metadata['total_chunks'] = len(self.document_chunks)
            
            with open(self.vector_store_path / "index_metadata.json", 'w', encoding='utf-8') as f:
                json.dump(self.index_metadata, f, indent=2, ensure_ascii=False)
            
            print("Índice salvo com sucesso")
            
        except Exception as e:
            print(f"Erro ao salvar índice: {e}")
    
    async def index_document(self, file_path: Path) -> bool:
        """
        Indexa um documento específico
        
        Args:
            file_path: Caminho para o arquivo .md
            
        Returns:
            True se indexado com sucesso
        """
        try:
            # Processa o documento
            document = document_processor.parse_markdown_file(file_path)
            
            if 'error' in document:
                print(f"Erro ao processar {file_path}: {document['error']}")
                return False
            
            # Verifica se documento já foi indexado (por hash)
            existing_chunks = [
                i for i, chunk in enumerate(self.document_chunks)
                if chunk['document_metadata']['file_path'] == str(file_path)
            ]
            
            # Remove chunks antigos se existirem
            if existing_chunks:
                self._remove_document_chunks(existing_chunks)
            
            # Divide em chunks
            chunks = document_processor.chunk_document(document)
            
            if not chunks:
                return False
            
            # Gera embeddings para os chunks
            chunk_texts = [chunk['content'] for chunk in chunks]
            embeddings = self.embedding_model.encode(chunk_texts, normalize_embeddings=True)
            
            # Adiciona ao índice FAISS
            self.vector_index.add(embeddings.astype('float32'))
            
            # Adiciona chunks à lista
            for i, chunk in enumerate(chunks):
                chunk['embedding_index'] = len(self.document_chunks) + i
                self.document_chunks.append(chunk)
            
            # Registra no banco de dados
            db_manager.add_indexed_document(
                file_path=str(file_path),
                file_hash=document['file_hash'],
                title=document['title'],
                content_preview=document_processor.get_content_preview(document['content']),
                chunk_count=len(chunks)
            )
            
            # Gera metadados adicionais com Gemini
            await self._enrich_document_metadata(document)
            
            print(f"Documento indexado: {document['title']} ({len(chunks)} chunks)")
            return True
            
        except Exception as e:
            print(f"Erro ao indexar documento {file_path}: {e}")
            return False
    
    def _remove_document_chunks(self, chunk_indices: List[int]):
        """Remove chunks de um documento do índice"""
        # FAISS não suporta remoção direta, então recria o índice
        remaining_chunks = []
        remaining_embeddings = []
        
        for i, chunk in enumerate(self.document_chunks):
            if i not in chunk_indices:
                remaining_chunks.append(chunk)
                # Recupera embedding do índice
                embedding = self.vector_index.reconstruct(i)
                remaining_embeddings.append(embedding)
        
        # Recria índice
        if remaining_embeddings:
            embedding_dim = len(remaining_embeddings[0])
            self.vector_index = faiss.IndexFlatIP(embedding_dim)
            embeddings_array = np.array(remaining_embeddings).astype('float32')
            self.vector_index.add(embeddings_array)
        else:
            self._create_new_index()
        
        self.document_chunks = remaining_chunks
    
    async def _enrich_document_metadata(self, document: Dict):
        """Enriquece metadados do documento usando Gemini"""
        try:
            # Gera resumo
            summary = await gemini_client.generate_summary(document['content'])
            
            # Extrai palavras-chave
            keywords = await gemini_client.extract_keywords(document['content'])
            
            # Categoriza conteúdo
            category = await gemini_client.categorize_content(document['content'])
            
            # Adiciona aos metadados
            document['ai_metadata'] = {
                'summary': summary,
                'keywords': keywords,
                'category': category,
                'generated_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"Erro ao enriquecer metadados: {e}")
    
    async def index_vault(self, force_reindex: bool = False) -> Dict:
        """
        Indexa todo o vault do Obsidian
        
        Args:
            force_reindex: Se True, reindexiza todos os documentos
            
        Returns:
            Estatísticas da indexação
        """
        print("Iniciando indexação do vault...")
        
        # Escaneia arquivos
        md_files = document_processor.scan_vault()
        
        if not md_files:
            return {
                'total_files': 0,
                'indexed_files': 0,
                'skipped_files': 0,
                'errors': 0
            }
        
        stats = {
            'total_files': len(md_files),
            'indexed_files': 0,
            'skipped_files': 0,
            'errors': 0
        }
        
        for file_path in md_files:
            try:
                # Verifica se precisa reindexar
                if not force_reindex:
                    current_hash = document_processor.get_file_hash(file_path)
                    # Aqui você poderia verificar se o hash mudou
                    # Por simplicidade, vamos indexar todos
                
                success = await self.index_document(file_path)
                
                if success:
                    stats['indexed_files'] += 1
                else:
                    stats['skipped_files'] += 1
                    
            except Exception as e:
                print(f"Erro ao processar {file_path}: {e}")
                stats['errors'] += 1
        
        # Salva índice
        self._save_index()
        
        # Atualiza metadados
        self.index_metadata['total_documents'] = stats['indexed_files']
        
        print(f"Indexação concluída: {stats['indexed_files']}/{stats['total_files']} arquivos")
        
        return stats
    
    def get_index_stats(self) -> Dict:
        """Retorna estatísticas do índice"""
        return {
            'total_documents': len(set(chunk['document_metadata']['file_path'] 
                                     for chunk in self.document_chunks)),
            'total_chunks': len(self.document_chunks),
            'index_metadata': self.index_metadata,
            'vault_path': str(config.OBSIDIAN_VAULT_PATH),
            'last_updated': self.index_metadata.get('updated_at', 'Nunca')
        }

# Instância global do serviço de indexação
indexing_service = IndexingService()
