# 🧠 Chat Inteligente Obsidian

Sistema de chat inteligente com memória contextual para suas notas do Obsidian, usando Google Gemini API, LangChain e busca semântica.

## ✨ Funcionalidades

### 🎯 Chat Inteligente
- **Memória Contextual**: Lembra conversas anteriores e mantém contexto
- **Busca Semântica**: Encontra informações relevantes nas suas notas
- **Respostas Precisas**: Baseadas no conteúdo real das suas notas
- **Múltiplos Tipos de Busca**: Semântica, por palavras-chave, categoria, tags

### 📚 Processamento de Notas
- **Suporte Completo ao Markdown**: Processa arquivos .md do Obsidian
- **Indexação Inteligente**: Cria embeddings vetoriais para busca eficiente
- **Metadados Automáticos**: Extrai categorias, palavras-chave e resumos
- **Chunking Inteligente**: Divide documentos grandes mantendo contexto

### 🔍 Busca Avançada
- **Busca Híbrida**: Combina busca semântica e por palavras-chave
- **Filtros por Categoria**: Direito, Neurociência, Marketing, etc.
- **Busca por Tags**: Encontra notas por tags específicas
- **Documentos Recentes**: Filtra por período de modificação

### 💾 Memória Persistente
- **Histórico de Conversas**: Salva todas as interações
- **Contexto Semântico**: Mantém relevância entre sessões
- **Banco de Dados Local**: SQLite para armazenamento eficiente

## 🚀 Instalação e Configuração

### Pré-requisitos
- Python 3.8+
- Google Gemini API Key
- Notas em formato Markdown (.md)

### 1. Clone e Instale Dependências
```bash
# Clone o repositório
cd "AUTO COMPLETE"

# Instale as dependências
pip install -r requirements.txt
```

### 2. Configuração da API
```bash
# Copie o arquivo de exemplo
copy .env.example .env

# Edite o arquivo .env e adicione sua API key do Gemini
GEMINI_API_KEY=sua_api_key_aqui
```

### 3. Prepare suas Notas
```bash
# Crie a pasta para suas notas (se não existir)
mkdir -p data/obsidian_notes

# Copie seus arquivos .md para a pasta
# Ou configure o caminho do seu vault no .env
```

### 4. Execute o Sistema
```bash
# Inicie o servidor
python -m backend.app

# Acesse no navegador
# http://localhost:8000
```

## 📖 Como Usar

### Primeira Execução
1. **Configure a API Key** do Google Gemini no arquivo `.env`
2. **Adicione suas notas** na pasta `data/obsidian_notes/`
3. **Indexe o vault** clicando em "Indexar Vault" na interface
4. **Comece a conversar** com suas notas!

### Tipos de Perguntas
```
# Busca geral
"Quais são os principais conceitos de Direito Constitucional?"

# Busca por categoria
"Mostre-me notas sobre neurociência"

# Busca por período
"Quais notas criei esta semana?"

# Conexões entre temas
"Qual a relação entre marketing e psicologia nas minhas notas?"

# Resumos
"Resuma os pontos principais sobre tecnologia"
```

### Upload de Novas Notas
- Use o botão "Upload Arquivo" para adicionar novos .md
- O sistema indexa automaticamente novos arquivos
- Reindexe periodicamente para manter tudo atualizado

## 🏗️ Arquitetura

### Backend (Python/FastAPI)
```
backend/
├── app.py                 # Servidor FastAPI principal
├── models/
│   ├── gemini_client.py   # Cliente Google Gemini
│   └── document_processor.py # Processamento Markdown
├── services/
│   ├── indexing_service.py    # Indexação vetorial
│   ├── search_service.py      # Busca semântica
│   └── chat_service.py        # Lógica do chat
└── utils/
    ├── config.py          # Configurações
    └── database.py        # Banco SQLite
```

### Frontend (HTML/CSS/JS)
```
frontend/
├── index.html            # Interface principal
├── css/style.css         # Estilos modernos
└── js/
    ├── app.js            # Lógica principal
    └── chat.js           # Interface do chat
```

### Dados
```
data/
├── obsidian_notes/       # Suas notas .md
├── vector_store/         # Índice FAISS
└── memory.db             # Banco de memória
```

## 🔧 Configurações Avançadas

### Variáveis de Ambiente (.env)
```bash
# API do Gemini
GEMINI_API_KEY=sua_chave_aqui

# Caminhos
OBSIDIAN_VAULT_PATH=./data/obsidian_notes
DATABASE_PATH=./data/memory.db
VECTOR_STORE_PATH=./data/vector_store

# Servidor
HOST=localhost
PORT=8000
DEBUG=True

# Chat
MAX_MEMORY_MESSAGES=50
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_TOKENS_PER_REQUEST=4000

# Modelo de Embeddings
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
```

### Personalização
- **Modelos de Embedding**: Altere `EMBEDDING_MODEL` para outros modelos
- **Tamanho dos Chunks**: Ajuste `CHUNK_SIZE` conforme suas notas
- **Memória do Chat**: Configure `MAX_MEMORY_MESSAGES`

## 📊 API Endpoints

### Chat
- `POST /api/chat` - Enviar mensagem
- `GET /api/chat/history/{session_id}` - Histórico
- `DELETE /api/chat/history/{session_id}` - Limpar histórico

### Busca
- `POST /api/search` - Busca geral
- `GET /api/search/categories/{category}` - Por categoria
- `GET /api/search/recent` - Documentos recentes

### Indexação
- `POST /api/index` - Indexar vault
- `GET /api/index/stats` - Estatísticas
- `GET /api/documents` - Listar documentos

### Sistema
- `GET /health` - Status do sistema
- `GET /api/config` - Configurações
- `POST /api/upload` - Upload de arquivo

## 🛠️ Desenvolvimento

### Estrutura de Dados
```python
# Mensagem do Chat
{
    "session_id": "session_123",
    "message": "Pergunta do usuário",
    "response": "Resposta do assistente",
    "sources": [{"title": "Nota.md", "similarity": 0.85}],
    "timestamp": "2024-01-01T12:00:00"
}

# Resultado de Busca
{
    "chunk_content": "Conteúdo relevante...",
    "document_title": "Título da Nota",
    "similarity_score": 0.85,
    "search_type": "hybrid"
}
```

### Extensões Possíveis
- **Integração com Obsidian**: Plugin nativo
- **Múltiplos Vaults**: Suporte a vários cofres
- **Exportação**: PDF, Word, etc.
- **Colaboração**: Compartilhamento de conversas
- **Análise**: Dashboards e métricas

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo LICENSE para detalhes.

## 🆘 Suporte

### Problemas Comuns
1. **API Key inválida**: Verifique se a chave do Gemini está correta
2. **Erro de indexação**: Verifique se os arquivos .md estão válidos
3. **Memória insuficiente**: Reduza `CHUNK_SIZE` ou `MAX_MEMORY_MESSAGES`

### Logs
```bash
# Logs do servidor
python -m backend.app

# Logs detalhados
DEBUG=True python -m backend.app
```

---

**Desenvolvido com ❤️ para potencializar seu conhecimento pessoal!**
