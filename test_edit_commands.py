#!/usr/bin/env python3
"""
Teste das funcionalidades de edição de notas
"""

import requests
import json

def test_create_note():
    """Testa criação de nova nota"""
    print("🆕 Testando criação de nova nota...")
    
    url = "http://localhost:8000/api/chat"
    data = {
        "message": "Crie uma nova nota sobre Inteligência Artificial com conceitos básicos e aplicações",
        "session_id": "test_edit_session"
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Comando detectado: {result.get('command_type', 'chat')}")
            print(f"📝 Resposta: {result['response'][:200]}...")
            return True
        else:
            print(f"❌ Erro: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_modify_note():
    """Testa modificação de nota existente"""
    print("✏️ Testando modificação de nota...")
    
    url = "http://localhost:8000/api/chat"
    data = {
        "message": "Modifique a nota 'Direito Constitucional' adicionando exemplos práticos dos princípios fundamentais",
        "session_id": "test_edit_session"
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Comando detectado: {result.get('command_type', 'chat')}")
            print(f"📝 Resposta: {result['response'][:200]}...")
            return True
        else:
            print(f"❌ Erro: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_add_content():
    """Testa adição de conteúdo"""
    print("➕ Testando adição de conteúdo...")
    
    url = "http://localhost:8000/api/chat"
    data = {
        "message": "Adicione uma seção de conclusão à nota sobre neurociência com um resumo dos pontos principais",
        "session_id": "test_edit_session"
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Comando detectado: {result.get('command_type', 'chat')}")
            print(f"📝 Resposta: {result['response'][:200]}...")
            return True
        else:
            print(f"❌ Erro: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_normal_chat():
    """Testa chat normal"""
    print("💬 Testando chat normal...")
    
    url = "http://localhost:8000/api/chat"
    data = {
        "message": "Quais são os principais conceitos de Direito Constitucional que estudei?",
        "session_id": "test_edit_session"
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Comando detectado: {result.get('command_type', 'chat')}")
            print(f"📝 Resposta: {result['response'][:200]}...")
            return True
        else:
            print(f"❌ Erro: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_list_notes():
    """Testa listagem de notas"""
    print("📋 Testando listagem de notas...")
    
    try:
        response = requests.get("http://localhost:8000/api/notes", timeout=10)
        if response.status_code == 200:
            result = response.json()
            notes = result.get('notes', [])
            print(f"✅ {len(notes)} notas encontradas")
            for note in notes[:3]:  # Mostra primeiras 3
                print(f"  📄 {note['title']}")
            return True
        else:
            print(f"❌ Erro: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def main():
    print("🧪 TESTANDO FUNCIONALIDADES DE EDIÇÃO")
    print("=" * 50)
    
    tests = [
        ("Listagem de Notas", test_list_notes),
        ("Chat Normal", test_normal_chat),
        ("Criação de Nota", test_create_note),
        ("Modificação de Nota", test_modify_note),
        ("Adição de Conteúdo", test_add_content),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔄 {test_name}")
        print("-" * 30)
        success = test_func()
        results.append((test_name, success))
        print()
    
    print("📊 RESULTADOS FINAIS")
    print("=" * 30)
    
    for test_name, success in results:
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"\n🎯 {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 TODOS OS TESTES PASSARAM!")
        print("💡 Seu sistema de edição está funcionando!")
    else:
        print("⚠️ Alguns testes falharam. Verifique os erros acima.")

if __name__ == "__main__":
    main()
