#!/usr/bin/env python3
"""
Setup simplificado para LLM local
Instala apenas dependências Python
"""

import subprocess
import sys
import json

def install_dependencies():
    """Instala dependências para LLM local"""
    print("📦 Instalando dependências para LLM local...")
    
    packages = [
        "transformers",
        "torch",
        "accelerate",
        "bitsandbytes",
        "sentencepiece"
    ]
    
    for package in packages:
        print(f"📥 Instalando {package}...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], check=True, capture_output=True)
            print(f"✅ {package} instalado")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erro ao instalar {package}: {e}")

def create_local_config():
    """Cria configuração para LLM local"""
    config = {
        "local_llm": {
            "enabled": True,
            "provider": "transformers",
            "model": "microsoft/DialoGPT-small",
            "device": "cpu",
            "max_length": 512,
            "temperature": 0.7
        },
        "hybrid_mode": {
            "enabled": True,
            "primary": "gemini",
            "fallback": "local",
            "use_local_for": [
                "simple_questions",
                "quick_responses",
                "backup"
            ]
        }
    }
    
    with open("local_llm_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("✅ Configuração salva em local_llm_config.json")

def main():
    """Função principal"""
    print("🤖 SETUP LLM LOCAL SIMPLIFICADO")
    print("=" * 40)
    
    # Instala dependências
    install_dependencies()
    
    # Cria configuração
    create_local_config()
    
    print("\n✅ SETUP CONCLUÍDO!")
    print("💡 Agora você pode usar o sistema híbrido")
    print("🚀 Execute: python sistema_hibrido.py")

if __name__ == "__main__":
    main()
