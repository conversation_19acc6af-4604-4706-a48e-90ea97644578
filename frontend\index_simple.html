<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Inteligente Obsidian</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
            background: white;
            border-radius: 12px;
            margin: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .sidebar {
            width: 320px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            padding: 20px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-header {
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
            background: white;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-messages {
            flex: 1;
            padding: 20px 30px;
            overflow-y: auto;
            scroll-behavior: smooth;
        }

        .message {
            display: flex;
            margin-bottom: 20px;
            animation: fadeInUp 0.3s ease;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .user-message .message-avatar {
            background: #667eea;
            color: white;
        }

        .assistant-message .message-avatar {
            background: #28a745;
            color: white;
        }

        .message-content {
            flex: 1;
            max-width: calc(100% - 55px);
        }

        .message-text {
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 18px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .user-message .message-text {
            background: #667eea;
            color: white;
            margin-left: auto;
            max-width: 80%;
        }

        .assistant-message .message-text {
            background: #f8f9fa;
            color: #495057;
        }

        .chat-input-area {
            padding: 20px 30px;
            border-top: 1px solid #e9ecef;
            background: white;
        }

        .input-container {
            display: flex;
            align-items: flex-end;
            gap: 10px;
            margin-bottom: 15px;
        }

        .input-container textarea {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            resize: none;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.4;
            max-height: 120px;
            transition: border-color 0.2s;
        }

        .input-container textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .input-container button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .input-container button:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .input-container button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .error {
            color: #dc3545;
            background: #f8d7da !important;
            border-left: 4px solid #dc3545;
        }

        .loading {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h2>🧠 Chat Obsidian</h2>
            <p>Sistema funcionando!</p>
            <p>API Status: <span id="api-status">Verificando...</span></p>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="chat-header">
                <h1>Chat Inteligente com suas Notas</h1>
            </div>
            
            <!-- Chat Container -->
            <div class="chat-container">
                <div id="chat-messages" class="chat-messages">
                    <!-- Mensagem de boas-vindas -->
                    <div class="message assistant-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot">🤖</i>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                Olá! Sou seu assistente inteligente para suas notas do Obsidian. 
                                Posso ajudá-lo a encontrar informações, criar novas notas e modificar conteúdo existente.
                                <br><br>
                                <strong>Exemplos de comandos:</strong><br>
                                • "Quais são os conceitos de Direito Constitucional?"<br>
                                • "Crie uma nova nota sobre Inteligência Artificial"<br>
                                • "Modifique a nota sobre neurociência adicionando exemplos"
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Input Area -->
                <div class="chat-input-area">
                    <div class="input-container">
                        <textarea 
                            id="message-input" 
                            placeholder="Digite sua pergunta ou comando..."
                            rows="1"
                        ></textarea>
                        <button id="send-btn">
                            ➤
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuração
        const API_BASE_URL = window.location.origin;
        let currentSessionId = 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
        let isProcessing = false;

        // Elementos
        const messagesContainer = document.getElementById('chat-messages');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-btn');
        const apiStatus = document.getElementById('api-status');

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM carregado');
            console.log('Message Input:', messageInput);
            console.log('Send Button:', sendButton);
            
            // Event listeners
            messageInput.addEventListener('keypress', function(e) {
                console.log('Tecla pressionada:', e.key);
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            sendButton.addEventListener('click', function() {
                console.log('Botão clicado');
                sendMessage();
            });
            
            // Auto-resize
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
            
            // Verifica API
            checkApiStatus();
        });

        async function checkApiStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    apiStatus.textContent = '✅ Online';
                    apiStatus.style.color = 'green';
                } else {
                    apiStatus.textContent = '⚠️ Problemas';
                    apiStatus.style.color = 'orange';
                }
            } catch (error) {
                apiStatus.textContent = '❌ Offline';
                apiStatus.style.color = 'red';
            }
        }

        function addMessage(role, content, isError = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            
            const avatar = role === 'user' ? '👤' : '🤖';
            
            messageDiv.innerHTML = `
                <div class="message-avatar">
                    ${avatar}
                </div>
                <div class="message-content">
                    <div class="message-text ${isError ? 'error' : ''}">
                        ${content.replace(/\n/g, '<br>')}
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showLoading() {
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'message assistant-message';
            loadingDiv.id = 'loading-message';
            loadingDiv.innerHTML = `
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <div class="message-text loading">
                        Pensando...
                    </div>
                </div>
            `;
            
            messagesContainer.appendChild(loadingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideLoading() {
            const loadingDiv = document.getElementById('loading-message');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }

        async function sendMessage() {
            console.log('sendMessage chamado');
            
            const message = messageInput.value.trim();
            console.log('Mensagem:', message);
            
            if (!message || isProcessing) {
                console.log('Mensagem vazia ou processando');
                return;
            }
            
            isProcessing = true;
            sendButton.disabled = true;
            sendButton.textContent = '⏳';
            
            // Adiciona mensagem do usuário
            addMessage('user', message);
            
            // Limpa input
            messageInput.value = '';
            messageInput.style.height = 'auto';
            
            // Mostra loading
            showLoading();
            
            try {
                console.log('Enviando para API...');
                
                const response = await fetch(`${API_BASE_URL}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId
                    })
                });
                
                console.log('Resposta recebida:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                console.log('Dados:', result);
                
                // Remove loading
                hideLoading();
                
                // Adiciona resposta
                addMessage('assistant', result.response);
                
                // Mostra informações adicionais
                if (result.command_type && result.command_type !== 'chat') {
                    addMessage('assistant', `🔧 Comando executado: ${result.command_type}`);
                }
                
                if (result.sources && result.sources.length > 0) {
                    addMessage('assistant', `📚 Baseado em ${result.sources.length} fonte(s)`);
                }
                
            } catch (error) {
                console.error('Erro:', error);
                hideLoading();
                addMessage('assistant', `❌ Erro: ${error.message}`, true);
            } finally {
                isProcessing = false;
                sendButton.disabled = false;
                sendButton.textContent = '➤';
                messageInput.focus();
            }
        }
    </script>
</body>
</html>
