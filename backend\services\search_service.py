import numpy as np
from typing import List, Dict, Optional, Tuple
import re
from datetime import datetime, timedelta

from .indexing_service import indexing_service
from ..utils.config import config

class SearchService:
    """Serviço de busca semântica nos documentos indexados"""
    
    def __init__(self):
        self.indexing_service = indexing_service
    
    async def semantic_search(self, query: str, top_k: int = 5, 
                            min_similarity: float = 0.3) -> List[Dict]:
        """
        Realiza busca semântica usando embeddings
        
        Args:
            query: Consulta do usuário
            top_k: Número máximo de resultados
            min_similarity: Similaridade mínima (0-1)
            
        Returns:
            Lista de chunks relevantes ordenados por similaridade
        """
        if not self.indexing_service.document_chunks:
            return []
        
        try:
            # Gera embedding da consulta
            query_embedding = self.indexing_service.embedding_model.encode(
                [query], normalize_embeddings=True
            )
            
            # Busca no índice FAISS
            similarities, indices = self.indexing_service.vector_index.search(
                query_embedding.astype('float32'), top_k
            )
            
            results = []
            for i, (similarity, idx) in enumerate(zip(similarities[0], indices[0])):
                if idx == -1 or similarity < min_similarity:
                    continue
                
                chunk = self.indexing_service.document_chunks[idx]
                
                result = {
                    'chunk_content': chunk['content'],
                    'similarity_score': float(similarity),
                    'document_title': chunk['document_metadata']['title'],
                    'document_path': chunk['document_metadata']['file_path'],
                    'chunk_index': chunk['chunk_index'],
                    'chunk_count': chunk['chunk_count'],
                    'document_metadata': chunk['document_metadata'],
                    'rank': i + 1
                }
                
                results.append(result)
            
            return results
            
        except Exception as e:
            print(f"Erro na busca semântica: {e}")
            return []
    
    async def hybrid_search(self, query: str, top_k: int = 5) -> List[Dict]:
        """
        Combina busca semântica com busca por palavras-chave
        
        Args:
            query: Consulta do usuário
            top_k: Número máximo de resultados
            
        Returns:
            Lista de resultados híbridos
        """
        # Busca semântica
        semantic_results = await self.semantic_search(query, top_k * 2)
        
        # Busca por palavras-chave
        keyword_results = self._keyword_search(query, top_k * 2)
        
        # Combina e reordena resultados
        combined_results = self._combine_search_results(
            semantic_results, keyword_results, top_k
        )
        
        return combined_results
    
    def _keyword_search(self, query: str, top_k: int = 10) -> List[Dict]:
        """Busca por palavras-chave no conteúdo"""
        if not self.indexing_service.document_chunks:
            return []
        
        query_words = self._extract_keywords(query.lower())
        results = []
        
        for i, chunk in enumerate(self.indexing_service.document_chunks):
            content_lower = chunk['content'].lower()
            title_lower = chunk['document_metadata']['title'].lower()
            
            # Calcula score baseado em matches
            score = 0
            matches = []
            
            for word in query_words:
                # Matches no conteúdo
                content_matches = len(re.findall(r'\b' + re.escape(word) + r'\b', content_lower))
                if content_matches > 0:
                    score += content_matches * 1.0
                    matches.append(f"'{word}' no conteúdo ({content_matches}x)")
                
                # Matches no título (peso maior)
                title_matches = len(re.findall(r'\b' + re.escape(word) + r'\b', title_lower))
                if title_matches > 0:
                    score += title_matches * 2.0
                    matches.append(f"'{word}' no título ({title_matches}x)")
                
                # Matches nas tags
                tags = chunk['document_metadata'].get('tags', [])
                for tag in tags:
                    if word in tag.lower():
                        score += 1.5
                        matches.append(f"'{word}' na tag '{tag}'")
            
            if score > 0:
                results.append({
                    'chunk_content': chunk['content'],
                    'keyword_score': score,
                    'document_title': chunk['document_metadata']['title'],
                    'document_path': chunk['document_metadata']['file_path'],
                    'chunk_index': chunk['chunk_index'],
                    'chunk_count': chunk['chunk_count'],
                    'document_metadata': chunk['document_metadata'],
                    'matches': matches
                })
        
        # Ordena por score
        results.sort(key=lambda x: x['keyword_score'], reverse=True)
        return results[:top_k]
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extrai palavras-chave relevantes do texto"""
        # Remove pontuação e divide em palavras
        words = re.findall(r'\b\w+\b', text.lower())
        
        # Remove palavras muito curtas e stop words básicas
        stop_words = {
            'o', 'a', 'os', 'as', 'um', 'uma', 'uns', 'umas', 'de', 'do', 'da', 'dos', 'das',
            'em', 'no', 'na', 'nos', 'nas', 'por', 'para', 'com', 'sem', 'sob', 'sobre',
            'e', 'ou', 'mas', 'que', 'se', 'quando', 'onde', 'como', 'porque', 'porque',
            'é', 'são', 'foi', 'foram', 'ser', 'estar', 'ter', 'haver', 'fazer', 'dizer',
            'muito', 'mais', 'menos', 'bem', 'mal', 'já', 'ainda', 'sempre', 'nunca',
            'sim', 'não', 'talvez', 'aqui', 'ali', 'lá', 'hoje', 'ontem', 'amanhã'
        }
        
        keywords = [word for word in words if len(word) > 2 and word not in stop_words]
        return keywords
    
    def _combine_search_results(self, semantic_results: List[Dict], 
                              keyword_results: List[Dict], top_k: int) -> List[Dict]:
        """Combina resultados de busca semântica e por palavras-chave"""
        combined = {}
        
        # Adiciona resultados semânticos
        for result in semantic_results:
            key = f"{result['document_path']}_{result['chunk_index']}"
            combined[key] = result
            combined[key]['final_score'] = result['similarity_score'] * 0.7  # Peso 70%
            combined[key]['search_type'] = 'semantic'
        
        # Adiciona/combina resultados de palavras-chave
        for result in keyword_results:
            key = f"{result['document_path']}_{result['chunk_index']}"
            
            # Normaliza keyword_score para 0-1
            normalized_keyword_score = min(result['keyword_score'] / 10.0, 1.0)
            
            if key in combined:
                # Combina scores
                combined[key]['final_score'] += normalized_keyword_score * 0.3  # Peso 30%
                combined[key]['search_type'] = 'hybrid'
                combined[key]['keyword_matches'] = result.get('matches', [])
            else:
                combined[key] = result
                combined[key]['final_score'] = normalized_keyword_score * 0.5  # Só keyword
                combined[key]['search_type'] = 'keyword'
        
        # Ordena por score final e retorna top_k
        results = list(combined.values())
        results.sort(key=lambda x: x['final_score'], reverse=True)
        
        return results[:top_k]
    
    async def search_by_category(self, category: str, top_k: int = 10) -> List[Dict]:
        """Busca documentos por categoria"""
        results = []
        
        for chunk in self.indexing_service.document_chunks:
            doc_metadata = chunk['document_metadata']
            
            # Verifica categoria nos metadados AI
            ai_metadata = doc_metadata.get('ai_metadata', {})
            doc_category = ai_metadata.get('category', '').lower()
            
            if category.lower() in doc_category:
                results.append({
                    'chunk_content': chunk['content'],
                    'document_title': doc_metadata['title'],
                    'document_path': doc_metadata['file_path'],
                    'category': doc_category,
                    'chunk_index': chunk['chunk_index'],
                    'document_metadata': doc_metadata
                })
        
        return results[:top_k]
    
    async def search_by_tags(self, tags: List[str], top_k: int = 10) -> List[Dict]:
        """Busca documentos por tags"""
        results = []
        
        for chunk in self.indexing_service.document_chunks:
            doc_metadata = chunk['document_metadata']
            doc_tags = [tag.lower() for tag in doc_metadata.get('tags', [])]
            
            # Verifica se alguma tag bate
            matching_tags = [tag for tag in tags if tag.lower() in doc_tags]
            
            if matching_tags:
                results.append({
                    'chunk_content': chunk['content'],
                    'document_title': doc_metadata['title'],
                    'document_path': doc_metadata['file_path'],
                    'matching_tags': matching_tags,
                    'all_tags': doc_metadata.get('tags', []),
                    'chunk_index': chunk['chunk_index'],
                    'document_metadata': doc_metadata
                })
        
        return results[:top_k]
    
    async def search_recent_documents(self, days: int = 7, top_k: int = 10) -> List[Dict]:
        """Busca documentos modificados recentemente"""
        cutoff_date = datetime.now() - timedelta(days=days)
        results = []
        
        seen_documents = set()
        
        for chunk in self.indexing_service.document_chunks:
            doc_metadata = chunk['document_metadata']
            doc_path = doc_metadata['file_path']
            
            # Evita duplicatas (um chunk por documento)
            if doc_path in seen_documents:
                continue
            
            modified_time = doc_metadata.get('modified_time')
            if modified_time and isinstance(modified_time, datetime):
                if modified_time > cutoff_date:
                    results.append({
                        'chunk_content': chunk['content'],
                        'document_title': doc_metadata['title'],
                        'document_path': doc_path,
                        'modified_time': modified_time.isoformat(),
                        'chunk_index': chunk['chunk_index'],
                        'document_metadata': doc_metadata
                    })
                    seen_documents.add(doc_path)
        
        # Ordena por data de modificação
        results.sort(key=lambda x: x['modified_time'], reverse=True)
        return results[:top_k]

# Instância global do serviço de busca
search_service = SearchService()
