#!/usr/bin/env python3
"""
Exemplo simples do sistema colaborativo
Mostra como Local + Gemini trabalham juntos
"""

import asyncio
from obsidian_multi_agent_system import ObsidianMultiAgentSystem

async def exemplo_simples():
    """Exemplo direto do sistema colaborativo"""
    print("🤝 EXEMPLO SISTEMA COLABORATIVO")
    print("=" * 35)
    print("Assistente Local + Gemini trabalhando juntos!")
    print()
    
    # Inicializa sistema (já vem com assistente local integrado)
    print("🔄 Inicializando sistema...")
    sistema = ObsidianMultiAgentSystem()
    
    print("\n✅ Sistema pronto!")
    print("🔥 FLUXO COLABORATIVO:")
    print("   1. ⚡ Assistente Local pré-processa SEMPRE")
    print("   2. 🧠 Gemini analisa com contexto SEMPRE") 
    print("   3. 🤝 Resposta colaborativa SEMPRE")
    print()
    
    # Pergunta de teste
    pergunta = "Explique o que é direito penal brasileiro"
    
    print(f"❓ PERGUNTA: {pergunta}")
    print("\n🔄 Processando colaborativamente...")
    
    # Sistema colaborativo em ação
    resposta = await sistema.chat(pergunta)
    
    print("\n" + "="*60)
    print("🎉 RESPOSTA COLABORATIVA:")
    print("="*60)
    print(resposta)

async def main():
    """Execução direta"""
    try:
        await exemplo_simples()
        
        print("\n" + "="*60)
        print("✅ SISTEMA COLABORATIVO FUNCIONANDO!")
        print("="*60)
        print("🔥 BENEFÍCIOS:")
        print("   ⚡ Assistente Local: Pré-processamento rápido")
        print("   🧠 Gemini: Análise inteligente e profunda")
        print("   🤝 Colaboração: Melhor resultado final")
        print("   📊 Métricas: Tempo e estatísticas detalhadas")
        print()
        print("💡 PRÓXIMOS PASSOS:")
        print("   1. Configure seus cofres do iCloud")
        print("   2. Use a interface web: streamlit run web_interface.py")
        print("   3. Aproveite o poder da colaboração!")
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        print("💡 Verifique se a API do Gemini está configurada")

if __name__ == "__main__":
    asyncio.run(main())
