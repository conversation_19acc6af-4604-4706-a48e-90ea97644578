from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import List, Optional, Dict
import uvicorn

# Imports dos serviços
from .services.chat_service import chat_service
from .services.indexing_service import indexing_service
from .services.search_service import search_service
from .services.command_analyzer import command_analyzer
from .utils.config import config
from .utils.database import db_manager

# Modelos Pydantic para API
class ChatMessage(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    session_id: str
    response: str
    sources: List[Dict]
    timestamp: str

class IndexRequest(BaseModel):
    force_reindex: bool = False

class SearchRequest(BaseModel):
    query: str
    search_type: str = "hybrid"  # hybrid, semantic, keyword
    top_k: int = 5

# Inicializa FastAPI
app = FastAPI(
    title="Chat Inteligente Obsidian",
    description="Sistema de chat inteligente com memória para notas do Obsidian",
    version="1.0.0"
)

# Configuração CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Em produção, especificar domínios
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Monta arquivos estáticos
app.mount("/static", StaticFiles(directory="frontend"), name="static")

@app.on_event("startup")
async def startup_event():
    """Inicialização do sistema"""
    try:
        print("🚀 Iniciando Chat Inteligente Obsidian...")

        # Valida configurações
        config.validate_config()
        print("✅ Configurações validadas")

        # Inicializa banco de dados
        db_manager.init_database()
        print("✅ Banco de dados inicializado")

        print("🎯 Sistema pronto! Acesse http://localhost:8000")

    except Exception as e:
        print(f"❌ Erro na inicialização: {e}")
        raise

# === ROTAS PRINCIPAIS ===

@app.get("/", response_class=HTMLResponse)
async def root():
    """Página principal"""
    try:
        with open("frontend/index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
            <body>
                <h1>Chat Inteligente Obsidian</h1>
                <p>Interface em desenvolvimento...</p>
                <p>Use a API em /docs para testar</p>
            </body>
        </html>
        """)

@app.get("/health")
async def health_check():
    """Verificação de saúde do sistema"""
    try:
        stats = indexing_service.get_index_stats()
        return {
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00",
            "index_stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# === ROTAS DO CHAT ===

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(chat_message: ChatMessage):
    """Endpoint principal do chat"""
    try:
        result = await chat_service.process_message(
            message=chat_message.message,
            session_id=chat_message.session_id
        )

        return ChatResponse(
            session_id=result['session_id'],
            response=result['response'],
            sources=result['sources'],
            timestamp=result['timestamp']
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/chat/history/{session_id}")
async def get_chat_history(session_id: str):
    """Obtém histórico de uma conversa"""
    try:
        history = await chat_service.get_conversation_history(session_id)
        return {"session_id": session_id, "history": history}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/chat/history/{session_id}")
async def clear_chat_history(session_id: str):
    """Limpa histórico de uma conversa"""
    try:
        success = await chat_service.clear_conversation(session_id)
        if success:
            return {"message": "Histórico limpo com sucesso"}
        else:
            raise HTTPException(status_code=404, detail="Conversa não encontrada")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# === ROTAS DE BUSCA ===

@app.post("/api/search")
async def search_endpoint(search_request: SearchRequest):
    """Endpoint de busca nos documentos"""
    try:
        if search_request.search_type == "semantic":
            results = await search_service.semantic_search(
                search_request.query, search_request.top_k
            )
        elif search_request.search_type == "keyword":
            results = search_service._keyword_search(
                search_request.query, search_request.top_k
            )
        else:  # hybrid
            results = await search_service.hybrid_search(
                search_request.query, search_request.top_k
            )

        return {"query": search_request.query, "results": results}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/search/categories/{category}")
async def search_by_category(category: str, top_k: int = 10):
    """Busca por categoria"""
    try:
        results = await search_service.search_by_category(category, top_k)
        return {"category": category, "results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/search/recent")
async def search_recent(days: int = 7, top_k: int = 10):
    """Busca documentos recentes"""
    try:
        results = await search_service.search_recent_documents(days, top_k)
        return {"days": days, "results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# === ROTAS DE INDEXAÇÃO ===

@app.post("/api/index")
async def index_vault(index_request: IndexRequest):
    """Indexa o vault do Obsidian"""
    try:
        stats = await indexing_service.index_vault(
            force_reindex=index_request.force_reindex
        )
        return {
            "message": "Indexação concluída",
            "stats": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/index/stats")
async def get_index_stats():
    """Obtém estatísticas do índice"""
    try:
        stats = indexing_service.get_index_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/documents")
async def list_documents():
    """Lista documentos indexados"""
    try:
        documents = db_manager.get_indexed_documents()
        return {"documents": documents}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# === ROTAS DE CONFIGURAÇÃO ===

@app.get("/api/config")
async def get_config():
    """Obtém configurações do sistema"""
    return {
        "vault_path": str(config.OBSIDIAN_VAULT_PATH),
        "max_memory_messages": config.MAX_MEMORY_MESSAGES,
        "chunk_size": config.CHUNK_SIZE,
        "embedding_model": config.EMBEDDING_MODEL,
        "gemini_model": config.GEMINI_MODEL
    }

@app.get("/api/stats")
async def get_system_stats():
    """Obtém estatísticas gerais do sistema"""
    try:
        stats = await chat_service.get_chat_statistics()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# === ROTAS DE EDIÇÃO DE NOTAS ===

@app.get("/api/notes")
async def list_notes():
    """Lista todas as notas do vault"""
    try:
        vault_path = config.OBSIDIAN_VAULT_PATH
        notes = []

        for md_file in vault_path.glob("*.md"):
            if md_file.is_file():
                try:
                    with open(md_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Extrai título e preview
                    lines = content.split('\n')
                    title = md_file.stem
                    preview = ""

                    # Procura por título no frontmatter ou primeiro heading
                    for line in lines:
                        if line.startswith('title:'):
                            title = line.split(':', 1)[1].strip().strip('"\'')
                            break
                        elif line.startswith('# '):
                            title = line[2:].strip()
                            break

                    # Gera preview
                    content_lines = [line for line in lines if not line.startswith('---') and line.strip()]
                    if content_lines:
                        preview = ' '.join(content_lines[:3])[:200] + "..."

                    notes.append({
                        'filename': md_file.name,
                        'title': title,
                        'preview': preview,
                        'path': str(md_file.relative_to(vault_path)),
                        'modified': md_file.stat().st_mtime
                    })
                except Exception:
                    continue

        # Ordena por data de modificação
        notes.sort(key=lambda x: x['modified'], reverse=True)

        return {"notes": notes}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/commands/examples")
async def get_command_examples():
    """Obtém exemplos de comandos de edição"""
    try:
        examples = command_analyzer.get_command_examples()
        return {
            "examples": examples,
            "help": {
                "modificar": "Use 'modifique a nota X' para editar uma nota existente",
                "criar": "Use 'crie uma nota sobre X' para criar uma nova nota",
                "adicionar": "Use 'adicione X à nota Y' para adicionar conteúdo"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/notes/{note_name}")
async def get_note_content(note_name: str):
    """Obtém o conteúdo de uma nota específica"""
    try:
        vault_path = config.OBSIDIAN_VAULT_PATH
        note_path = vault_path / f"{note_name}.md"

        if not note_path.exists():
            # Tenta busca por similaridade
            for md_file in vault_path.glob("*.md"):
                if note_name.lower() in md_file.stem.lower():
                    note_path = md_file
                    break
            else:
                raise HTTPException(status_code=404, detail=f"Nota '{note_name}' não encontrada")

        with open(note_path, 'r', encoding='utf-8') as f:
            content = f.read()

        return {
            "filename": note_path.name,
            "content": content,
            "path": str(note_path.relative_to(vault_path))
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# === UPLOAD DE ARQUIVOS ===

@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload de arquivo .md para o vault"""
    try:
        if not file.filename.endswith('.md'):
            raise HTTPException(status_code=400, detail="Apenas arquivos .md são aceitos")

        # Salva arquivo no vault
        file_path = config.OBSIDIAN_VAULT_PATH / file.filename

        with open(file_path, 'wb') as f:
            content = await file.read()
            f.write(content)

        # Indexa o arquivo
        success = await indexing_service.index_document(file_path)

        if success:
            return {"message": f"Arquivo {file.filename} enviado e indexado com sucesso"}
        else:
            return {"message": f"Arquivo {file.filename} enviado, mas houve erro na indexação"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "backend.app:app",
        host=config.HOST,
        port=config.PORT,
        reload=config.DEBUG
    )
