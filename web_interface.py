#!/usr/bin/env python3
"""
Interface Web para o Sistema Multi-Agente Obsidian
Usando Streamlit para interface amigável
"""

import streamlit as st
import asyncio
import json
from pathlib import Path
import pandas as pd
from obsidian_multi_agent_system import ObsidianMultiAgentSystem
from sistema_auto_update import SistemaAutoUpdate

# Configuração da página
st.set_page_config(
    page_title="🧠 Sistema Multi-Agente Obsidian",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS customizado
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }

    .vault-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #667eea;
        margin: 0.5rem 0;
    }

    .agent-response {
        background: #e8f4fd;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #2196F3;
        margin: 1rem 0;
    }

    .success-message {
        background: #d4edda;
        color: #155724;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #c3e6cb;
    }

    .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 5px;
        border: 1px solid #f5c6cb;
    }
</style>
""", unsafe_allow_html=True)

# Inicialização do sistema
@st.cache_resource
def init_system():
    """Inicializa o sistema multi-agente"""
    return ObsidianMultiAgentSystem()

def main():
    """Interface principal"""

    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🧠 Sistema Multi-Agente para Obsidian</h1>
        <p>🤝 Local + Gemini SEMPRE juntos | 🔄 Auto-Update de notas</p>
    </div>
    """, unsafe_allow_html=True)

    # Inicializa sistema
    if 'system' not in st.session_state:
        with st.spinner("🚀 Inicializando sistema..."):
            st.session_state.system = init_system()
            st.session_state.vaults_added = []
            st.session_state.indexed = False

    # Sidebar - Configuração de Cofres
    with st.sidebar:
        st.header("📁 Configuração de Cofres")

        # Adicionar novo cofre
        with st.expander("➕ Adicionar Cofre", expanded=True):
            vault_name = st.text_input("Nome do Cofre", placeholder="Ex: Direito")
            vault_path = st.text_input("Caminho do Cofre", placeholder="C:/Users/<USER>/Documents/Obsidian/Direito")
            vault_desc = st.text_area("Descrição", placeholder="Descrição do conteúdo do cofre")

            if st.button("Adicionar Cofre", type="primary"):
                if vault_name and vault_path:
                    success = st.session_state.system.add_vault(vault_name, vault_path, vault_desc)
                    if success:
                        st.session_state.vaults_added.append({
                            "name": vault_name,
                            "path": vault_path,
                            "description": vault_desc
                        })
                        st.success(f"✅ Cofre '{vault_name}' adicionado!")
                        st.session_state.indexed = False
                    else:
                        st.error("❌ Erro ao adicionar cofre. Verifique o caminho.")
                else:
                    st.error("❌ Preencha nome e caminho do cofre.")

        # Lista de cofres adicionados
        if st.session_state.vaults_added:
            st.subheader("📋 Cofres Configurados")
            for vault in st.session_state.vaults_added:
                st.markdown(f"""
                <div class="vault-card">
                    <strong>{vault['name']}</strong><br>
                    <small>{vault['description']}</small><br>
                    <code>{vault['path']}</code>
                </div>
                """, unsafe_allow_html=True)

        # Botão de indexação
        if st.session_state.vaults_added and not st.session_state.indexed:
            if st.button("🔄 Indexar Todos os Cofres", type="secondary"):
                with st.spinner("🔄 Indexando cofres..."):
                    asyncio.run(st.session_state.system.index_all_vaults())
                    st.session_state.indexed = True
                    st.success("✅ Indexação concluída!")

        # Auto-Update
        st.subheader("🔄 Atualização Automática")

        if 'auto_update_system' not in st.session_state:
            st.session_state.auto_update_system = None
            st.session_state.auto_update_active = False

        col_auto1, col_auto2 = st.columns(2)

        with col_auto1:
            if st.button("🔄 Ativar Auto-Update", disabled=st.session_state.auto_update_active):
                if st.session_state.vaults_added:
                    # Inicializa sistema de auto-update
                    st.session_state.auto_update_system = SistemaAutoUpdate()

                    # Adiciona cofres ao monitoramento
                    for vault in st.session_state.vaults_added:
                        st.session_state.auto_update_system.adicionar_cofre_monitorado(
                            vault['name'], vault['path'], vault['description']
                        )

                    # Inicia monitoramento
                    st.session_state.auto_update_system.iniciar_monitoramento()
                    st.session_state.auto_update_active = True

                    st.success("✅ Auto-Update ATIVADO!")
                    st.info("🔄 Suas notas serão atualizadas automaticamente!")
                else:
                    st.error("❌ Adicione cofres primeiro")

        with col_auto2:
            if st.button("⏹️ Parar Auto-Update", disabled=not st.session_state.auto_update_active):
                if st.session_state.auto_update_system:
                    st.session_state.auto_update_system.parar_monitoramento()
                    st.session_state.auto_update_active = False
                    st.success("⏹️ Auto-Update PARADO!")

        # Status do auto-update
        if st.session_state.auto_update_active and st.session_state.auto_update_system:
            status = st.session_state.auto_update_system.status_monitoramento()

            st.markdown("**📊 Status Auto-Update:**")
            st.markdown(f"• **Ativo:** {'✅ Sim' if status['ativo'] else '❌ Não'}")
            st.markdown(f"• **Cofres monitorados:** {status['cofres_monitorados']}")
            st.markdown(f"• **Verificação:** A cada {status['intervalo_verificacao']}s")

            if status['cofres']:
                st.markdown("**📁 Cofres:**")
                for nome, info in status['cofres'].items():
                    st.markdown(f"  - {nome}: {info['arquivos']} arquivos")

        # Estatísticas
        if st.session_state.vaults_added:
            st.subheader("📊 Estatísticas")
            stats = st.session_state.system.get_vault_stats()
            st.metric("Total de Cofres", stats["total_vaults"])
            st.metric("Total de Arquivos", stats["total_files"])

            # Indicador de auto-update
            if st.session_state.auto_update_active:
                st.success("🔄 Auto-Update ATIVO")
            else:
                st.info("⏸️ Auto-Update INATIVO")

    # Área principal
    col1, col2 = st.columns([2, 1])

    with col1:
        st.header("💬 Chat com seus Cofres")

        # Área de chat
        if 'messages' not in st.session_state:
            st.session_state.messages = []

        # Exibe mensagens
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])

        # Input do usuário
        if prompt := st.chat_input("Digite sua pergunta ou comando..."):
            if not st.session_state.vaults_added:
                st.error("❌ Adicione pelo menos um cofre antes de conversar.")
            elif not st.session_state.indexed:
                st.error("❌ Indexe os cofres antes de conversar.")
            else:
                # Adiciona mensagem do usuário
                st.session_state.messages.append({"role": "user", "content": prompt})
                with st.chat_message("user"):
                    st.markdown(prompt)

                # Resposta do assistente
                with st.chat_message("assistant"):
                    with st.spinner("🤖 Processando..."):
                        # Usa auto-update se ativo, senão sistema normal
                        if st.session_state.auto_update_active and st.session_state.auto_update_system:
                            response = asyncio.run(st.session_state.auto_update_system.chat_com_auto_update(prompt))
                        else:
                            response = asyncio.run(st.session_state.system.chat(prompt))

                        st.markdown(response)
                        st.session_state.messages.append({"role": "assistant", "content": response})

    with col2:
        st.header("🛠️ Ferramentas")

        # Exemplos de comandos
        st.subheader("💡 Exemplos de Comandos")

        examples = [
            "Analise todos os meus cofres",
            "Encontre conexões entre direito e neurociência",
            "Sugira melhorias para organização",
            "Crie uma nota sobre ética em IA",
            "Quais são os temas principais em cada cofre?",
            "Modifique a nota sobre contratos",
            "Adicione exemplos à nota de marketing"
        ]

        for example in examples:
            if st.button(f"💬 {example}", key=f"example_{hash(example)}"):
                if st.session_state.vaults_added and st.session_state.indexed:
                    # Simula clique no exemplo
                    st.session_state.messages.append({"role": "user", "content": example})
                    response = asyncio.run(st.session_state.system.chat(example))
                    st.session_state.messages.append({"role": "assistant", "content": response})
                    st.rerun()
                else:
                    st.error("❌ Configure e indexe os cofres primeiro.")

        # Ações rápidas
        st.subheader("⚡ Ações Rápidas")

        if st.button("🔍 Analisar Cofres"):
            if st.session_state.vaults_added and st.session_state.indexed:
                with st.spinner("Analisando..."):
                    response = asyncio.run(st.session_state.system.chat("Faça uma análise completa de todos os meus cofres"))
                    st.markdown(f'<div class="agent-response">{response}</div>', unsafe_allow_html=True)

        if st.button("🔗 Encontrar Conexões"):
            if st.session_state.vaults_added and st.session_state.indexed:
                with st.spinner("Buscando conexões..."):
                    response = asyncio.run(st.session_state.system.chat("Encontre todas as conexões possíveis entre meus cofres"))
                    st.markdown(f'<div class="agent-response">{response}</div>', unsafe_allow_html=True)

        if st.button("💡 Sugestões de Melhoria"):
            if st.session_state.vaults_added and st.session_state.indexed:
                with st.spinner("Gerando sugestões..."):
                    response = asyncio.run(st.session_state.system.chat("Dê sugestões detalhadas para melhorar meus cofres"))
                    st.markdown(f'<div class="agent-response">{response}</div>', unsafe_allow_html=True)

    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666;">
        🧠 Sistema Multi-Agente Obsidian | Powered by LangChain + Gemini
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
