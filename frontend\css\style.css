/* Reset e Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    height: 100vh;
    overflow: hidden;
}

/* Layout Principal */
.app-container {
    display: flex;
    height: 100vh;
    background: white;
    border-radius: 12px;
    margin: 10px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 320px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: white;
}

.sidebar-header h2 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.4rem;
}

.sidebar-header h2 i {
    color: #667eea;
    margin-right: 8px;
}

.sidebar-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.sidebar-content h3 {
    color: #495057;
    font-size: 1rem;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Seções da Sidebar */
.stats-section, .actions-section, .search-section {
    margin-bottom: 30px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 8px 0;
}

.stat-label {
    color: #6c757d;
}

.stat-value {
    font-weight: 600;
    color: #495057;
}

/* Busca Rápida */
.search-input-group {
    display: flex;
    margin-bottom: 15px;
}

.search-input-group input {
    flex: 1;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 6px 0 0 6px;
    font-size: 14px;
}

.search-input-group button {
    border-radius: 0 6px 6px 0;
    border-left: none;
}

.search-results {
    max-height: 200px;
    overflow-y: auto;
}

.search-result-item {
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.search-result-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.search-result-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 4px;
}

.search-result-preview {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.chat-header {
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
}

.chat-header h1 {
    color: #495057;
    font-size: 1.5rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    padding: 20px 30px;
    overflow-y: auto;
    scroll-behavior: smooth;
}

/* Mensagens */
.message {
    display: flex;
    margin-bottom: 20px;
    animation: fadeInUp 0.3s ease;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: #667eea;
    color: white;
}

.assistant-message .message-avatar {
    background: #28a745;
    color: white;
}

.message-content {
    flex: 1;
    max-width: calc(100% - 55px);
}

.message-text {
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 18px;
    line-height: 1.5;
    word-wrap: break-word;
}

.user-message .message-text {
    background: #667eea;
    color: white;
    margin-left: auto;
    max-width: 80%;
}

.assistant-message .message-text {
    background: #f8f9fa;
    color: #495057;
}

.message-sources {
    margin-top: 10px;
    padding: 10px;
    background: #e9ecef;
    border-radius: 8px;
    font-size: 12px;
}

.source-item {
    margin-bottom: 5px;
    color: #6c757d;
}

.source-item:last-child {
    margin-bottom: 0;
}

/* Input Area */
.chat-input-area {
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
    background: white;
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: 10px;
    margin-bottom: 15px;
}

.input-container textarea {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    max-height: 120px;
    transition: border-color 0.2s;
}

.input-container textarea:focus {
    outline: none;
    border-color: #667eea;
}

.input-container button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Sugestões */
.input-suggestions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.suggestion-btn {
    padding: 8px 15px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    color: #495057;
}

.suggestion-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

/* Botões */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    text-align: right;
}

.config-section {
    margin-bottom: 20px;
}

.config-section h4 {
    color: #495057;
    margin-bottom: 10px;
}

.config-section input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
}

.config-section small {
    color: #6c757d;
    font-size: 12px;
}

.config-section label {
    display: block;
    margin-bottom: 5px;
    color: #495057;
}

/* Status Indicator */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #dc3545;
}

.status-dot.online {
    background: #28a745;
}

.status-dot.loading {
    background: #ffc107;
    animation: pulse 1s infinite;
}

/* Loading */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    z-index: 2000;
    align-items: center;
    justify-content: center;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    text-align: center;
    color: #667eea;
}

.loading-spinner i {
    font-size: 48px;
    margin-bottom: 15px;
}

/* Toast */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 3000;
}

.toast {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-left: 4px solid #28a745;
    animation: slideInRight 0.3s ease;
}

.toast.error {
    border-left-color: #dc3545;
}

.toast.warning {
    border-left-color: #ffc107;
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsivo */
@media (max-width: 768px) {
    .app-container {
        margin: 0;
        border-radius: 0;
        height: 100vh;
    }
    
    .sidebar {
        width: 280px;
    }
    
    .chat-header h1 {
        font-size: 1.2rem;
    }
    
    .header-actions {
        flex-direction: column;
        gap: 5px;
    }
    
    .input-suggestions {
        display: none;
    }
}
