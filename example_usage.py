#!/usr/bin/env python3
"""
Exemplo Prático de Uso
Sistema Multi-Agente Obsidian

Este arquivo demonstra como usar o sistema com seus cofres reais.
"""

import asyncio
import json
from pathlib import Path
from obsidian_multi_agent_system import ObsidianMultiAgentSystem

async def example_basic_usage():
    """Exemplo básico de uso"""
    print("🚀 EXEMPLO BÁSICO DE USO")
    print("=" * 40)
    
    # 1. Inicializa o sistema
    print("1. 🤖 Inicializando sistema...")
    system = ObsidianMultiAgentSystem()
    
    # 2. Adiciona cofres (substitua pelos seus caminhos reais)
    print("\n2. 📁 Adicionando cofres...")
    
    # IMPORTANTE: Substitua estes caminhos pelos seus cofres reais!
    vaults_config = [
        {
            "name": "Direito",
            "path": "C:/Users/<USER>/Documents/Obsidian/Direito",
            "description": "Notas sobre direito constitucional, civil e penal"
        },
        {
            "name": "Neurociência", 
            "path": "C:/Users/<USER>/Documents/Obsidian/Neurociencia",
            "description": "Estudos sobre neurociência, psicologia e comportamento"
        },
        {
            "name": "Marketing",
            "path": "C:/Users/<USER>/Documents/Obsidian/Marketing", 
            "description": "Estratégias de marketing digital e tradicional"
        }
    ]
    
    # Adiciona cada cofre
    for vault in vaults_config:
        success = system.add_vault(vault["name"], vault["path"], vault["description"])
        if success:
            print(f"   ✅ {vault['name']}: Adicionado")
        else:
            print(f"   ❌ {vault['name']}: Caminho não encontrado - {vault['path']}")
    
    # 3. Indexa todos os cofres
    print("\n3. 🔄 Indexando cofres...")
    await system.index_all_vaults()
    
    # 4. Exemplos de conversas
    print("\n4. 💬 Exemplos de conversas:")
    print("-" * 30)
    
    # Exemplo 1: Análise geral
    print("\n🔍 Análise Geral:")
    response = await system.chat("Faça uma análise completa de todos os meus cofres")
    print(f"Resposta: {response[:200]}...")
    
    # Exemplo 2: Busca específica
    print("\n🔍 Busca Específica:")
    response = await system.chat("Quais são os conceitos principais de direito constitucional?")
    print(f"Resposta: {response[:200]}...")
    
    # Exemplo 3: Conexões
    print("\n🔗 Encontrar Conexões:")
    response = await system.chat("Encontre conexões entre neurociência e direito")
    print(f"Resposta: {response[:200]}...")
    
    # Exemplo 4: Sugestões
    print("\n💡 Sugestões:")
    response = await system.chat("Sugira melhorias para organizar meus cofres")
    print(f"Resposta: {response[:200]}...")
    
    # 5. Estatísticas
    print("\n5. 📊 Estatísticas:")
    stats = system.get_vault_stats()
    print(f"   • Total de cofres: {stats['total_vaults']}")
    print(f"   • Total de arquivos: {stats['total_files']}")
    
    for vault in stats['vaults']:
        print(f"   • {vault['name']}: {vault['file_count']} arquivos")

async def example_advanced_usage():
    """Exemplo avançado com comandos específicos"""
    print("\n\n🎯 EXEMPLO AVANÇADO")
    print("=" * 40)
    
    system = ObsidianMultiAgentSystem()
    
    # Carrega configuração de arquivo
    config_file = Path("config_example.json")
    if config_file.exists():
        print("📋 Carregando configuração do arquivo...")
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # Adiciona cofres da configuração
        for vault in config['vaults']:
            system.add_vault(vault['name'], vault['path'], vault['description'])
        
        await system.index_all_vaults()
        
        # Comandos avançados
        advanced_commands = [
            "Analise os padrões de escrita em cada cofre",
            "Identifique lacunas de conhecimento entre os cofres",
            "Sugira novas notas para conectar diferentes áreas",
            "Crie um mapa conceitual dos principais temas",
            "Encontre contradições ou inconsistências entre notas",
            "Sugira tags para melhor organização",
            "Identifique notas que precisam de atualização"
        ]
        
        print("\n🎯 Executando comandos avançados:")
        for i, command in enumerate(advanced_commands, 1):
            print(f"\n{i}. {command}")
            response = await system.chat(command)
            print(f"   Resposta: {response[:150]}...")
    
    else:
        print("❌ Arquivo config_example.json não encontrado")
        print("💡 Execute setup_obsidian_system.py primeiro")

async def example_interactive_session():
    """Exemplo de sessão interativa"""
    print("\n\n💬 SESSÃO INTERATIVA")
    print("=" * 40)
    print("Digite 'sair' para terminar")
    
    system = ObsidianMultiAgentSystem()
    
    # Configuração rápida para demo
    demo_vaults = [
        ("Demo", "data/obsidian_notes", "Notas de demonstração")
    ]
    
    for name, path, desc in demo_vaults:
        system.add_vault(name, path, desc)
    
    await system.index_all_vaults()
    
    print("\n✅ Sistema pronto! Faça suas perguntas:")
    
    while True:
        try:
            user_input = input("\n💬 Você: ").strip()
            
            if user_input.lower() in ['sair', 'exit', 'quit']:
                print("👋 Até logo!")
                break
            
            if not user_input:
                continue
            
            print("🤖 Assistente: Processando...")
            response = await system.chat(user_input)
            print(f"🤖 Assistente: {response}")
            
        except KeyboardInterrupt:
            print("\n\n👋 Sessão encerrada pelo usuário")
            break
        except Exception as e:
            print(f"❌ Erro: {e}")

def print_usage_instructions():
    """Imprime instruções de uso"""
    print("📋 INSTRUÇÕES DE USO")
    print("=" * 40)
    print()
    print("1. 🔧 CONFIGURAÇÃO INICIAL:")
    print("   • Execute: python setup_obsidian_system.py")
    print("   • Edite config_example.json com seus caminhos")
    print()
    print("2. 🚀 EXECUÇÃO:")
    print("   • Interface Web: streamlit run web_interface.py")
    print("   • Linha de comando: python example_usage.py")
    print("   • Sistema direto: python obsidian_multi_agent_system.py")
    print()
    print("3. 💬 COMANDOS DISPONÍVEIS:")
    print("   • 'Analise todos os meus cofres'")
    print("   • 'Encontre conexões entre [área1] e [área2]'")
    print("   • 'Sugira melhorias para organização'")
    print("   • 'Crie uma nota sobre [tópico]'")
    print("   • 'Modifique a nota [nome] adicionando [conteúdo]'")
    print("   • 'Quais são os temas principais em [cofre]?'")
    print()
    print("4. 🎯 DICAS:")
    print("   • Use linguagem natural")
    print("   • Seja específico nos comandos")
    print("   • Experimente diferentes tipos de perguntas")
    print("   • O sistema aprende com o contexto")

async def main():
    """Função principal"""
    print("🧠 SISTEMA MULTI-AGENTE OBSIDIAN")
    print("🎯 Exemplos de Uso")
    print("=" * 50)
    
    # Menu de opções
    print("\nEscolha uma opção:")
    print("1. 📋 Ver instruções de uso")
    print("2. 🚀 Exemplo básico")
    print("3. 🎯 Exemplo avançado")
    print("4. 💬 Sessão interativa")
    print("5. 🌐 Abrir interface web")
    
    try:
        choice = input("\nOpção (1-5): ").strip()
        
        if choice == "1":
            print_usage_instructions()
        elif choice == "2":
            await example_basic_usage()
        elif choice == "3":
            await example_advanced_usage()
        elif choice == "4":
            await example_interactive_session()
        elif choice == "5":
            print("🌐 Abrindo interface web...")
            print("💡 Execute: streamlit run web_interface.py")
            import subprocess
            subprocess.run(["streamlit", "run", "web_interface.py"])
        else:
            print("❌ Opção inválida")
            
    except KeyboardInterrupt:
        print("\n\n👋 Programa encerrado pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro: {e}")

if __name__ == "__main__":
    asyncio.run(main())
