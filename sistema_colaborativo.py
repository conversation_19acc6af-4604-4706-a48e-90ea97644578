#!/usr/bin/env python3
"""
Sistema Colaborativo: LLM Local + Gemini
O LLM local AJUDA o Gemini, não substitui
"""

import asyncio
import json
import os
import re
from pathlib import Path
from typing import List, Dict, Any
from collections import Counter

# Configuração
GOOGLE_API_KEY = "AIzaSyCq1rBFLFqX04jQ2ejd5Tkbane-lTvIsVU"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

class AssistenteLocal:
    """LLM Local que AJUDA o Gemini"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.loaded = False
        self.load_model()
    
    def load_model(self):
        """Carrega modelo local pequeno para tarefas auxiliares"""
        try:
            print("🔄 Carregando assistente local...")
            
            from transformers import AutoTokenizer, AutoModelForCausalLM
            
            # Modelo pequeno para tarefas auxiliares
            model_name = "microsoft/DialoGPT-small"
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(model_name)
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            self.loaded = True
            print("✅ Assistente local carregado!")
            
        except Exception as e:
            print(f"⚠️ Assistente local não disponível: {e}")
            self.loaded = False
    
    def extrair_topicos(self, texto: str) -> List[str]:
        """Extrai tópicos principais do texto"""
        if not texto:
            return []
        
        # Palavras-chave jurídicas comuns
        palavras_juridicas = [
            'direito', 'lei', 'artigo', 'código', 'constituição', 'jurisprudência',
            'processo', 'crime', 'contrato', 'responsabilidade', 'princípio',
            'tribunal', 'recurso', 'sentença', 'decisão', 'norma', 'regulamento'
        ]
        
        # Encontra palavras-chave no texto
        texto_lower = texto.lower()
        topicos_encontrados = []
        
        for palavra in palavras_juridicas:
            if palavra in texto_lower:
                # Busca contexto ao redor da palavra
                pattern = rf'.{{0,30}}{palavra}.{{0,30}}'
                matches = re.findall(pattern, texto_lower)
                if matches:
                    topicos_encontrados.extend(matches)
        
        return list(set(topicos_encontrados))[:10]  # Máximo 10 tópicos
    
    def contar_elementos(self, texto: str) -> Dict[str, int]:
        """Conta elementos estruturais do texto"""
        if not texto:
            return {}
        
        return {
            'paragrafos': len(texto.split('\n\n')),
            'linhas': len(texto.split('\n')),
            'palavras': len(texto.split()),
            'caracteres': len(texto),
            'artigos_mencionados': len(re.findall(r'art\.?\s*\d+', texto.lower())),
            'leis_mencionadas': len(re.findall(r'lei\s*n?º?\s*\d+', texto.lower()))
        }
    
    def identificar_categorias(self, texto: str) -> List[str]:
        """Identifica categorias jurídicas no texto"""
        categorias = {
            'penal': ['crime', 'penal', 'delito', 'pena', 'prisão', 'homicídio'],
            'civil': ['contrato', 'civil', 'responsabilidade', 'danos', 'obrigação'],
            'constitucional': ['constituição', 'constitucional', 'direitos fundamentais'],
            'processual': ['processo', 'procedimento', 'recurso', 'sentença'],
            'trabalhista': ['trabalho', 'trabalhista', 'clt', 'empregado'],
            'tributário': ['tributo', 'imposto', 'fiscal', 'tributário'],
            'administrativo': ['administrativo', 'servidor', 'público', 'licitação']
        }
        
        texto_lower = texto.lower()
        categorias_encontradas = []
        
        for categoria, palavras in categorias.items():
            if any(palavra in texto_lower for palavra in palavras):
                categorias_encontradas.append(categoria)
        
        return categorias_encontradas
    
    def pre_processar_documentos(self, documentos: List[Dict]) -> Dict[str, Any]:
        """Pré-processa documentos para ajudar o Gemini"""
        if not documentos:
            return {}
        
        print("🔍 Assistente local analisando documentos...")
        
        # Combina todo o texto
        texto_completo = "\n".join([doc.get('content', '') for doc in documentos])
        
        # Análise estrutural
        analise = {
            'total_documentos': len(documentos),
            'estatisticas': self.contar_elementos(texto_completo),
            'topicos_principais': self.extrair_topicos(texto_completo),
            'categorias_identificadas': self.identificar_categorias(texto_completo),
            'documentos_por_categoria': {}
        }
        
        # Analisa cada documento
        for doc in documentos:
            categorias = self.identificar_categorias(doc.get('content', ''))
            fonte = doc.get('metadata', {}).get('source', 'desconhecido')
            
            for categoria in categorias:
                if categoria not in analise['documentos_por_categoria']:
                    analise['documentos_por_categoria'][categoria] = []
                analise['documentos_por_categoria'][categoria].append(fonte)
        
        print(f"✅ Análise concluída: {len(analise['topicos_principais'])} tópicos, {len(analise['categorias_identificadas'])} categorias")
        
        return analise

class EspecialistaGemini:
    """Gemini como especialista principal"""
    
    def __init__(self):
        self.llm = None
        self.loaded = False
        self.load_model()
    
    def load_model(self):
        """Carrega Gemini"""
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            self.llm = ChatGoogleGenerativeAI(
                model="gemini-1.5-pro",
                temperature=0.3,
                google_api_key=GOOGLE_API_KEY
            )
            
            self.loaded = True
            print("✅ Especialista Gemini carregado!")
            
        except Exception as e:
            print(f"❌ Erro ao carregar Gemini: {e}")
            self.loaded = False
    
    async def analisar_com_contexto(self, pergunta: str, analise_local: Dict, documentos: List[Dict]) -> str:
        """Analisa usando o trabalho do assistente local"""
        if not self.loaded:
            return "❌ Gemini não disponível"
        
        # Monta prompt enriquecido com análise local
        contexto_local = f"""
ANÁLISE PRÉVIA DO ASSISTENTE LOCAL:
- Total de documentos: {analise_local.get('total_documentos', 0)}
- Categorias identificadas: {', '.join(analise_local.get('categorias_identificadas', []))}
- Tópicos principais: {', '.join(analise_local.get('topicos_principais', [])[:5])}
- Estatísticas: {analise_local.get('estatisticas', {})}

DOCUMENTOS RELEVANTES:
"""
        
        # Adiciona conteúdo dos documentos mais relevantes
        for i, doc in enumerate(documentos[:3]):
            contexto_local += f"\nDocumento {i+1}: {doc.get('content', '')[:500]}...\n"
        
        prompt_completo = f"""
{contexto_local}

PERGUNTA DO USUÁRIO: {pergunta}

Como especialista em direito, analise a pergunta considerando:
1. A análise prévia do assistente local
2. Os documentos fornecidos
3. Seu conhecimento jurídico especializado

Forneça uma resposta completa e contextualizada em português.
"""
        
        try:
            from langchain.schema import HumanMessage
            
            response = await self.llm.ainvoke([HumanMessage(content=prompt_completo)])
            return response.content
            
        except Exception as e:
            return f"❌ Erro no Gemini: {e}"

class SistemaColaborativo:
    """Sistema onde Local AJUDA Gemini"""
    
    def __init__(self):
        print("🤝 SISTEMA COLABORATIVO")
        print("=" * 30)
        print("LLM Local + Gemini trabalhando juntos!")
        print()
        
        # Inicializa componentes
        self.assistente = AssistenteLocal()
        self.especialista = EspecialistaGemini()
        
        # Status
        print(f"🏠 Assistente Local: {'✅ Ativo' if self.assistente.loaded else '❌ Inativo'}")
        print(f"🌐 Especialista Gemini: {'✅ Ativo' if self.especialista.loaded else '❌ Inativo'}")
        
        if self.especialista.loaded:
            print("✅ Sistema colaborativo pronto!")
        else:
            print("⚠️ Funcionando apenas com assistente local")
    
    async def processar_pergunta(self, pergunta: str, documentos: List[Dict] = None) -> str:
        """Processa pergunta com colaboração Local + Gemini"""
        
        print(f"💬 Pergunta: {pergunta}")
        
        if not documentos:
            documentos = []
        
        # Etapa 1: Assistente Local pré-processa
        if self.assistente.loaded and documentos:
            print("🔄 Etapa 1: Assistente local analisando...")
            analise_local = self.assistente.pre_processar_documentos(documentos)
        else:
            print("⚠️ Pulando análise local (sem documentos ou assistente)")
            analise_local = {}
        
        # Etapa 2: Gemini analisa com contexto do Local
        if self.especialista.loaded:
            print("🔄 Etapa 2: Gemini analisando com contexto...")
            resposta = await self.especialista.analisar_com_contexto(pergunta, analise_local, documentos)
            return f"🤝 [Colaborativo] {resposta}"
        else:
            return "❌ Especialista Gemini não disponível"

async def demo_colaborativo():
    """Demonstração do sistema colaborativo"""
    print("🎯 DEMO SISTEMA COLABORATIVO")
    print("=" * 35)
    
    # Inicializa sistema
    sistema = SistemaColaborativo()
    
    # Documentos de exemplo
    docs_exemplo = [
        {
            'content': 'O direito penal brasileiro estabelece que o crime de homicídio está previsto no artigo 121 do Código Penal. A pena varia conforme as circunstâncias.',
            'metadata': {'source': 'direito_penal.md'}
        },
        {
            'content': 'O processo penal deve respeitar o princípio da presunção de inocência, conforme artigo 5º da Constituição Federal.',
            'metadata': {'source': 'processo_penal.md'}
        }
    ]
    
    # Perguntas de teste
    perguntas = [
        "O que é homicídio no direito penal?",
        "Explique o princípio da presunção de inocência",
        "Quais são as principais categorias jurídicas mencionadas?"
    ]
    
    for pergunta in perguntas:
        print(f"\n{'='*50}")
        resposta = await sistema.processar_pergunta(pergunta, docs_exemplo)
        print(f"🤖 {resposta[:300]}...")

async def main():
    """Menu principal"""
    print("🤝 SISTEMA COLABORATIVO OBSIDIAN")
    print("=" * 40)
    print("LLM Local AJUDA Gemini - Trabalho em equipe!")
    print()
    
    print("Escolha uma opção:")
    print("1. 🧪 Demo colaborativo")
    print("2. 📁 Testar com cofres reais")
    print("3. ⚙️ Configurar sistema")
    
    try:
        choice = input("\nOpção (1-3): ").strip()
        
        if choice == "1":
            await demo_colaborativo()
        elif choice == "2":
            print("📁 Testando com cofres reais...")
            # Aqui integraria com seus cofres
            print("💡 Implemente integração com cofres do iCloud")
        elif choice == "3":
            print("⚙️ Configurações do sistema colaborativo")
            print("💡 Ajuste parâmetros de colaboração")
        else:
            print("❌ Opção inválida")
            
    except KeyboardInterrupt:
        print("\n👋 Sistema encerrado")

if __name__ == "__main__":
    asyncio.run(main())
