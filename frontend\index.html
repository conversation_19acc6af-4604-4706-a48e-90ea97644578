<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Inteligente Obsidian</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-brain"></i> Chat Obsidian</h2>
                <button id="new-chat-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Nova Conversa
                </button>
            </div>
            
            <div class="sidebar-content">
                <!-- Estatísticas -->
                <div class="stats-section">
                    <h3>Estatísticas</h3>
                    <div class="stat-item">
                        <span class="stat-label">Documentos:</span>
                        <span id="docs-count" class="stat-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Chunks:</span>
                        <span id="chunks-count" class="stat-value">0</span>
                    </div>
                </div>
                
                <!-- Ações rápidas -->
                <div class="actions-section">
                    <h3>Ações</h3>
                    <button id="index-vault-btn" class="btn btn-secondary">
                        <i class="fas fa-sync"></i> Indexar Vault
                    </button>
                    <button id="upload-file-btn" class="btn btn-secondary">
                        <i class="fas fa-upload"></i> Upload Arquivo
                    </button>
                    <input type="file" id="file-input" accept=".md" style="display: none;">
                </div>
                
                <!-- Busca rápida -->
                <div class="search-section">
                    <h3>Busca Rápida</h3>
                    <div class="search-input-group">
                        <input type="text" id="quick-search" placeholder="Buscar nas notas...">
                        <button id="search-btn" class="btn btn-icon">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div id="search-results" class="search-results"></div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="chat-header">
                <h1>Chat Inteligente com suas Notas</h1>
                <div class="header-actions">
                    <button id="clear-chat-btn" class="btn btn-outline">
                        <i class="fas fa-trash"></i> Limpar Chat
                    </button>
                    <button id="settings-btn" class="btn btn-outline">
                        <i class="fas fa-cog"></i> Configurações
                    </button>
                </div>
            </div>
            
            <!-- Chat Container -->
            <div class="chat-container">
                <div id="chat-messages" class="chat-messages">
                    <!-- Mensagem de boas-vindas -->
                    <div class="message assistant-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                Olá! Sou seu assistente inteligente para suas notas do Obsidian. 
                                Posso ajudá-lo a encontrar informações, fazer conexões entre suas notas 
                                e responder perguntas baseadas no seu conhecimento pessoal.
                                <br><br>
                                <strong>Como usar:</strong>
                                <ul>
                                    <li>Faça perguntas sobre qualquer tópico das suas notas</li>
                                    <li>Peça resumos ou conexões entre conceitos</li>
                                    <li>Busque por categoria: "Mostre-me notas sobre Direito"</li>
                                    <li>Busque por período: "Quais notas criei esta semana?"</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Input Area -->
                <div class="chat-input-area">
                    <div class="input-container">
                        <textarea 
                            id="message-input" 
                            placeholder="Digite sua pergunta sobre suas notas..."
                            rows="1"
                        ></textarea>
                        <button id="send-btn" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="input-suggestions">
                        <button class="suggestion-btn" data-text="Quais são os principais conceitos de Direito que estudei?">
                            📚 Conceitos de Direito
                        </button>
                        <button class="suggestion-btn" data-text="Mostre-me conexões entre neurociência e marketing">
                            🧠 Neurociência + Marketing
                        </button>
                        <button class="suggestion-btn" data-text="Quais notas criei esta semana?">
                            📅 Notas Recentes
                        </button>
                        <button class="suggestion-btn" data-text="Resuma os pontos principais sobre tecnologia">
                            💻 Resumo Tecnologia
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal de Configurações -->
    <div id="settings-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Configurações</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="config-section">
                    <h4>Caminho do Vault</h4>
                    <input type="text" id="vault-path" readonly>
                    <small>Coloque seus arquivos .md na pasta: data/obsidian_notes/</small>
                </div>
                
                <div class="config-section">
                    <h4>Configurações do Chat</h4>
                    <label>
                        Máximo de mensagens na memória:
                        <input type="number" id="max-memory" min="10" max="100" value="50">
                    </label>
                </div>
                
                <div class="config-section">
                    <h4>Status da API</h4>
                    <div id="api-status" class="status-indicator">
                        <span class="status-dot"></span>
                        <span class="status-text">Verificando...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">Fechar</button>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Processando...</p>
        </div>
    </div>
    
    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>
    
    <script src="/static/js/app.js"></script>
    <script src="/static/js/chat.js"></script>
</body>
</html>
