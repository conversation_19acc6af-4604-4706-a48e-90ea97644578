# 🧠 Sistema Multi-Agente para Obsidian

**Sistema profissional de IA para análise, conexão e edição de múltiplos cofres Obsidian usando LangChain + Gemini API**

## 🎯 **O QUE ESTE SISTEMA FAZ**

### ✨ **Funcionalidades Principais**
- 🔍 **Analisa múltiplos cofres** Obsidian simultaneamente
- 🔗 **Encontra conexões** entre notas de diferentes áreas
- ✏️ **Edita e cria conteúdo** automaticamente
- 💡 **Sugere melhorias** e organizações
- 💬 **Chat inteligente** com contexto de todos os cofres
- 🤖 **Sistema multi-agente** especializado

### 🤖 **Agentes Especializados**
1. **Analyzer Agent**: Analisa padrões e conteúdo dos cofres
2. **Connection Finder**: Encontra relações entre diferentes notas
3. **Content Editor**: Edita e cria novo conteúdo
4. **Suggestion Agent**: Propõe melhorias e organizações
5. **Coordinator Agent**: Coordena e decide qual agente usar

## 🚀 **INSTALAÇÃO RÁPIDA**

### **1. Configuração Automática**
```bash
# Execute o setup automático
python setup_obsidian_system.py
```

### **2. Instalação Manual**
```bash
# Instale dependências
pip install -r requirements_obsidian.txt

# Configure ambiente
cp .env.example .env
# Edite .env com sua API Key do Gemini
```

### **3. Interface Web (Recomendado)**
```bash
# Inicie a interface web
streamlit run web_interface.py

# Acesse: http://localhost:8501
```

## 📁 **CONFIGURAÇÃO DOS COFRES**

### **Método 1: Interface Web**
1. Abra `http://localhost:8501`
2. Use a sidebar "Configuração de Cofres"
3. Adicione nome, caminho e descrição
4. Clique "Indexar Todos os Cofres"

### **Método 2: Arquivo de Configuração**
Edite `config_example.json`:
```json
{
  "vaults": [
    {
      "name": "Direito",
      "path": "C:/Users/<USER>/Documents/Obsidian/Direito",
      "description": "Notas sobre direito constitucional e civil"
    },
    {
      "name": "Neurociência",
      "path": "C:/Users/<USER>/Documents/Obsidian/Neurociencia", 
      "description": "Estudos sobre neurociência e psicologia"
    }
  ]
}
```

### **Método 3: Código Python**
```python
from obsidian_multi_agent_system import ObsidianMultiAgentSystem

system = ObsidianMultiAgentSystem()
system.add_vault("Direito", "/caminho/para/direito", "Notas jurídicas")
system.add_vault("Neurociência", "/caminho/para/neuro", "Estudos de neurociência")

await system.index_all_vaults()
```

## 💬 **EXEMPLOS DE USO**

### **Análise de Cofres**
```
"Analise todos os meus cofres e me dê um resumo"
"Quais são os temas principais em cada cofre?"
"Identifique padrões de escrita entre os cofres"
```

### **Encontrar Conexões**
```
"Encontre conexões entre direito e neurociência"
"Sugira links entre notas de marketing e psicologia"
"Quais conceitos aparecem em múltiplos cofres?"
```

### **Edição de Conteúdo**
```
"Crie uma nova nota sobre ética em IA"
"Modifique a nota sobre contratos adicionando exemplos"
"Adicione uma seção de conclusão à nota de marketing"
```

### **Sugestões de Melhoria**
```
"Sugira melhorias para organizar meus cofres"
"Que novas notas devo criar para conectar as áreas?"
"Como posso melhorar a estrutura das minhas notas?"
```

## 🛠️ **ARQUIVOS PRINCIPAIS**

### **Sistema Core**
- `obsidian_multi_agent_system.py` - Sistema principal multi-agente
- `web_interface.py` - Interface web com Streamlit
- `example_usage.py` - Exemplos práticos de uso

### **Configuração**
- `setup_obsidian_system.py` - Setup automático
- `requirements_obsidian.txt` - Dependências
- `config_example.json` - Configuração de cofres
- `.env` - Variáveis de ambiente

### **Scripts de Execução**
- `run_web.bat` - Inicia interface web (Windows)
- `run_system.bat` - Execução direta (Windows)
- `run_system.sh` - Execução direta (Linux/Mac)

## 🎯 **FORMAS DE USAR**

### **1. Interface Web (Mais Fácil)**
```bash
streamlit run web_interface.py
```
- ✅ Interface visual amigável
- ✅ Configuração via sidebar
- ✅ Chat em tempo real
- ✅ Exemplos de comandos

### **2. Linha de Comando**
```bash
python example_usage.py
```
- ✅ Menu interativo
- ✅ Exemplos pré-configurados
- ✅ Sessão de chat

### **3. Integração em Código**
```python
import asyncio
from obsidian_multi_agent_system import ObsidianMultiAgentSystem

async def main():
    system = ObsidianMultiAgentSystem()
    # Adicione seus cofres
    # Execute comandos
    response = await system.chat("Sua pergunta")
    print(response)

asyncio.run(main())
```

## 🔧 **CONFIGURAÇÕES AVANÇADAS**

### **Variáveis de Ambiente (.env)**
```env
GOOGLE_API_KEY=sua_api_key_aqui
OBSIDIAN_VAULT_PATH=C:/Users/<USER>/Documents/Obsidian
LOG_LEVEL=INFO
MAX_TOKENS=4000
TEMPERATURE=0.3
```

### **Parâmetros do Sistema**
```python
# Personalização avançada
system = ObsidianMultiAgentSystem()
system.llm.temperature = 0.5  # Criatividade
system.embeddings.model = "models/embedding-001"  # Modelo de embedding
```

## 📊 **MONITORAMENTO**

### **Estatísticas dos Cofres**
```python
stats = system.get_vault_stats()
print(f"Total de cofres: {stats['total_vaults']}")
print(f"Total de arquivos: {stats['total_files']}")
```

### **Logs do Sistema**
- Console mostra progresso da indexação
- Decisões dos agentes são logadas
- Erros são capturados e reportados

## 🆘 **SOLUÇÃO DE PROBLEMAS**

### **Erro: Cofre não encontrado**
- ✅ Verifique se o caminho está correto
- ✅ Use barras normais `/` ou duplas `\\`
- ✅ Certifique-se que existem arquivos `.md`

### **Erro: API Key inválida**
- ✅ Verifique se a API Key do Gemini está correta
- ✅ Confirme que está no arquivo `.env`
- ✅ Teste a API Key no Google AI Studio

### **Erro: Dependências**
- ✅ Execute `pip install -r requirements_obsidian.txt`
- ✅ Use Python 3.8+
- ✅ Considere usar ambiente virtual

### **Performance lenta**
- ✅ Reduza `chunk_size` para cofres grandes
- ✅ Use menos cofres simultaneamente
- ✅ Verifique conexão com internet

## 🎉 **RECURSOS AVANÇADOS**

### **Busca Semântica**
- Indexação automática com FAISS
- Embeddings do Google Generative AI
- Busca por similaridade contextual

### **Memória Conversacional**
- Mantém contexto da conversa
- Referências a mensagens anteriores
- Aprendizado durante a sessão

### **Multi-Threading**
- Processamento assíncrono
- Múltiplos agentes simultâneos
- Interface responsiva

## 📈 **ROADMAP FUTURO**

### **Próximas Funcionalidades**
- [ ] Plugin nativo para Obsidian
- [ ] Sincronização automática
- [ ] Análise de imagens em notas
- [ ] Suporte a áudio/vídeo
- [ ] Integração com calendário
- [ ] Backup automático
- [ ] Colaboração multi-usuário

## 🤝 **CONTRIBUIÇÃO**

Este sistema foi desenvolvido especificamente para Alexandre, mas pode ser adaptado para outros usuários:

1. Fork o projeto
2. Adapte os caminhos dos cofres
3. Customize os agentes conforme necessário
4. Teste com seus dados
5. Compartilhe melhorias

## 📞 **SUPORTE**

Para dúvidas ou problemas:
1. Verifique este README
2. Execute `python example_usage.py` para testes
3. Use a interface web para debug visual
4. Verifique logs no console

---

**🎯 Sistema desenvolvido com LangChain + Gemini API para máxima qualidade e performance!**
