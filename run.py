#!/usr/bin/env python3
"""
Script de inicialização do Chat Inteligente Obsidian
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_python_version():
    """Verifica se a versão do Python é compatível"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ é necessário. Versão atual:", sys.version)
        return False
    print("✅ Python version:", sys.version.split()[0])
    return True

def check_dependencies():
    """Verifica se as dependências estão instaladas"""
    try:
        import fastapi
        import uvicorn
        import google.generativeai
        import sentence_transformers
        import faiss
        print("✅ Dependências principais encontradas")
        return True
    except ImportError as e:
        print(f"❌ Dependência não encontrada: {e}")
        print("💡 Execute: pip install -r requirements.txt")
        return False

def check_env_file():
    """Verifica se o arquivo .env existe"""
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  Arquivo .env não encontrado")
        print("📝 Criando arquivo .env a partir do exemplo...")
        
        example_file = Path(".env.example")
        if example_file.exists():
            import shutil
            shutil.copy(example_file, env_file)
            print("✅ Arquivo .env criado")
            print("🔑 IMPORTANTE: Configure sua GEMINI_API_KEY no arquivo .env")
            return False
        else:
            print("❌ Arquivo .env.example não encontrado")
            return False
    
    # Verifica se a API key está configurada
    with open(env_file, 'r') as f:
        content = f.read()
        if "your_gemini_api_key_here" in content:
            print("⚠️  GEMINI_API_KEY não configurada no arquivo .env")
            print("🔑 Configure sua API key do Google Gemini antes de continuar")
            return False
    
    print("✅ Arquivo .env configurado")
    return True

def create_directories():
    """Cria diretórios necessários"""
    directories = [
        "data",
        "data/obsidian_notes",
        "data/vector_store"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Diretórios criados")

def check_notes():
    """Verifica se existem notas para indexar"""
    notes_dir = Path("data/obsidian_notes")
    md_files = list(notes_dir.glob("*.md"))
    
    if not md_files:
        print("📝 Nenhuma nota encontrada em data/obsidian_notes/")
        print("💡 Adicione seus arquivos .md nesta pasta ou use o upload na interface")
    else:
        print(f"✅ {len(md_files)} notas encontradas")
    
    return len(md_files) > 0

def install_dependencies():
    """Instala dependências automaticamente"""
    print("📦 Instalando dependências...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependências instaladas com sucesso")
        return True
    except subprocess.CalledProcessError:
        print("❌ Erro ao instalar dependências")
        return False

def start_server():
    """Inicia o servidor"""
    print("🚀 Iniciando servidor...")
    print("📍 URL: http://localhost:8000")
    print("⏹️  Para parar: Ctrl+C")
    print("-" * 50)
    
    try:
        # Aguarda um pouco e abre o navegador
        def open_browser():
            time.sleep(3)
            webbrowser.open("http://localhost:8000")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Inicia o servidor
        os.system("python -m backend.app")
        
    except KeyboardInterrupt:
        print("\n👋 Servidor parado pelo usuário")
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")

def main():
    """Função principal"""
    print("🧠 Chat Inteligente Obsidian")
    print("=" * 40)
    
    # Verificações
    if not check_python_version():
        return
    
    create_directories()
    
    if not check_dependencies():
        install_deps = input("Deseja instalar as dependências automaticamente? (s/n): ")
        if install_deps.lower() in ['s', 'sim', 'y', 'yes']:
            if not install_dependencies():
                return
        else:
            print("💡 Execute: pip install -r requirements.txt")
            return
    
    if not check_env_file():
        print("\n🔧 Configure o arquivo .env e execute novamente")
        return
    
    check_notes()
    
    print("\n🎯 Tudo pronto! Iniciando sistema...")
    print("\n📚 Primeiros passos:")
    print("1. Acesse http://localhost:8000")
    print("2. Clique em 'Indexar Vault' para processar suas notas")
    print("3. Comece a conversar com suas notas!")
    print()
    
    start_server()

if __name__ == "__main__":
    main()
