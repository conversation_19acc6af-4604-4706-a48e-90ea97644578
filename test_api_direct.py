#!/usr/bin/env python3
"""
Teste direto da API para verificar se está funcionando
"""

import requests
import json

def test_simple_chat():
    """Testa chat simples"""
    print("💬 Testando chat simples...")
    
    url = "http://localhost:8000/api/chat"
    data = {
        "message": "Ol<PERSON>, como você está?",
        "session_id": "test_simple"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Resposta: {result['response'][:100]}...")
            return True
        else:
            print(f"❌ Erro: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_health():
    """Testa endpoint de saúde"""
    print("🏥 Testando saúde da API...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status: {result['status']}")
            return True
        else:
            print(f"❌ Erro: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def main():
    print("🧪 TESTE DIRETO DA API")
    print("=" * 30)
    
    if test_health():
        print("\n" + "=" * 30)
        test_simple_chat()
    else:
        print("❌ API não está funcionando")

if __name__ == "__main__":
    main()
