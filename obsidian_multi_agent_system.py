#!/usr/bin/env python3
"""
Sistema Multi-Agente para Múltiplos Cofres Obsidian
Baseado em LangChain + Gemini API

Funcionalidades:
- Análise de múltiplos cofres
- Sugestões de conexões
- Edição colaborativa de arquivos
- Chat inteligente contextual
"""

import os
import json
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

# LangChain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage, SystemMessage
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain.chains import ConversationalRetrieval<PERSON>hain
from langchain.memory import ConversationBufferMemory

# Configuração
GOOGLE_API_KEY = "AIzaSyCq1rBFLFqX04jQ2ejd5Tkbane-lTvIsVU"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

@dataclass
class ObsidianVault:
    """Representa um cofre do Obsidian"""
    name: str
    path: str
    description: str
    files: List[str] = None

    def __post_init__(self):
        if self.files is None:
            self.files = []

@dataclass
class Connection:
    """Representa uma conexão entre notas"""
    source_file: str
    target_file: str
    source_vault: str
    target_vault: str
    connection_type: str
    confidence: float
    description: str

class ObsidianMultiAgentSystem:
    """Sistema principal multi-agente para Obsidian"""

    def __init__(self):
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-pro",
            temperature=0.3,
            google_api_key=GOOGLE_API_KEY
        )

        self.embeddings = GoogleGenerativeAIEmbeddings(
            model="models/embedding-001",
            google_api_key=GOOGLE_API_KEY
        )

        self.vaults: List[ObsidianVault] = []
        self.vectorstore = None
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )

        # Agentes especializados
        self.agents = {
            "analyzer": VaultAnalyzerAgent(self.llm),
            "connector": ConnectionFinderAgent(self.llm),
            "editor": ContentEditorAgent(self.llm),
            "suggester": SuggestionAgent(self.llm),
            "coordinator": CoordinatorAgent(self.llm)
        }

        print("🤖 Sistema Multi-Agente Obsidian inicializado!")
        print(f"✅ Gemini API configurada")
        print(f"✅ {len(self.agents)} agentes especializados carregados")

    def add_vault(self, name: str, path: str, description: str):
        """Adiciona um cofre ao sistema"""
        vault_path = Path(path)
        if not vault_path.exists():
            print(f"❌ Caminho não encontrado: {path}")
            return False

        vault = ObsidianVault(name, path, description)
        vault.files = self._scan_vault_files(vault_path)
        self.vaults.append(vault)

        print(f"✅ Cofre '{name}' adicionado: {len(vault.files)} arquivos")
        return True

    def _scan_vault_files(self, vault_path: Path) -> List[str]:
        """Escaneia arquivos markdown no cofre"""
        files = []
        for file_path in vault_path.rglob("*.md"):
            if not any(part.startswith('.') for part in file_path.parts):
                files.append(str(file_path))
        return files

    async def index_all_vaults(self):
        """Indexa todos os cofres para busca semântica"""
        print("🔄 Indexando todos os cofres...")

        all_documents = []
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200
        )

        for vault in self.vaults:
            print(f"📁 Processando cofre: {vault.name}")

            for file_path in vault.files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Adiciona metadados
                    metadata = {
                        "source": file_path,
                        "vault": vault.name,
                        "filename": Path(file_path).name
                    }

                    # Divide em chunks
                    chunks = text_splitter.split_text(content)
                    for chunk in chunks:
                        all_documents.append({
                            "content": chunk,
                            "metadata": metadata
                        })

                except Exception as e:
                    print(f"⚠️ Erro ao processar {file_path}: {e}")

        # Cria vectorstore
        texts = [doc["content"] for doc in all_documents]
        metadatas = [doc["metadata"] for doc in all_documents]

        self.vectorstore = FAISS.from_texts(
            texts,
            self.embeddings,
            metadatas=metadatas
        )

        print(f"✅ Indexação concluída: {len(all_documents)} chunks processados")

    async def chat(self, message: str) -> str:
        """Interface de chat principal"""
        print(f"💬 Usuário: {message}")

        # Coordenador decide qual agente usar
        agent_decision = await self.agents["coordinator"].decide_agent(message)
        selected_agent = agent_decision["agent"]

        print(f"🎯 Agente selecionado: {selected_agent}")

        # Busca contexto relevante
        context = await self._get_relevant_context(message)

        # Executa ação do agente
        if selected_agent == "analyzer":
            response = await self.agents["analyzer"].analyze_vaults(self.vaults, message)
        elif selected_agent == "connector":
            response = await self.agents["connector"].find_connections(self.vaults, message, context)
        elif selected_agent == "editor":
            response = await self.agents["editor"].edit_content(message, context)
        elif selected_agent == "suggester":
            response = await self.agents["suggester"].suggest_improvements(self.vaults, message, context)
        else:
            response = await self._general_chat(message, context)

        return response

    async def _get_relevant_context(self, query: str, k: int = 5) -> List[Dict]:
        """Busca contexto relevante nos cofres"""
        if not self.vectorstore:
            return []

        docs = self.vectorstore.similarity_search(query, k=k)
        context = []

        for doc in docs:
            context.append({
                "content": doc.page_content,
                "metadata": doc.metadata
            })

        return context

    async def _general_chat(self, message: str, context: List[Dict]) -> str:
        """Chat geral com contexto"""
        context_text = "\n\n".join([
            f"[{ctx['metadata']['vault']}] {ctx['metadata']['filename']}:\n{ctx['content']}"
            for ctx in context
        ])

        prompt = f"""
        Você é um assistente inteligente para múltiplos cofres Obsidian.

        Contexto dos cofres:
        {context_text}

        Pergunta do usuário: {message}

        Responda de forma útil e contextualizada, citando as fontes quando relevante.
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return response.content

    def get_vault_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas dos cofres"""
        stats = {
            "total_vaults": len(self.vaults),
            "total_files": sum(len(vault.files) for vault in self.vaults),
            "vaults": []
        }

        for vault in self.vaults:
            vault_stats = {
                "name": vault.name,
                "description": vault.description,
                "file_count": len(vault.files),
                "path": vault.path
            }
            stats["vaults"].append(vault_stats)

        return stats

# Agentes especializados
class VaultAnalyzerAgent:
    """Agente para análise de cofres"""

    def __init__(self, llm):
        self.llm = llm

    async def analyze_vaults(self, vaults: List[ObsidianVault], query: str) -> str:
        vault_info = "\n".join([
            f"- {vault.name}: {len(vault.files)} arquivos ({vault.description})"
            for vault in vaults
        ])

        prompt = f"""
        Analise os seguintes cofres Obsidian:

        {vault_info}

        Consulta: {query}

        Forneça uma análise detalhada incluindo:
        1. Visão geral dos cofres
        2. Padrões identificados
        3. Áreas de conhecimento cobertas
        4. Sugestões de organização
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return f"📊 **Análise dos Cofres:**\n\n{response.content}"

class ConnectionFinderAgent:
    """Agente para encontrar conexões entre notas"""

    def __init__(self, llm):
        self.llm = llm

    async def find_connections(self, vaults: List[ObsidianVault], query: str, context: List[Dict]) -> str:
        context_text = "\n".join([
            f"[{ctx['metadata']['vault']}] {ctx['metadata']['filename']}"
            for ctx in context
        ])

        prompt = f"""
        Encontre conexões entre as seguintes notas de diferentes cofres:

        {context_text}

        Consulta: {query}

        Identifique:
        1. Conexões conceituais
        2. Temas relacionados
        3. Oportunidades de links
        4. Sugestões de novas notas

        Formato: [[Nome da Nota]] para links internos
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return f"🔗 **Conexões Encontradas:**\n\n{response.content}"

class ContentEditorAgent:
    """Agente para edição de conteúdo"""

    def __init__(self, llm):
        self.llm = llm

    async def edit_content(self, request: str, context: List[Dict]) -> str:
        prompt = f"""
        Você é um editor especializado em markdown para Obsidian.

        Solicitação: {request}

        Contexto disponível: {len(context)} documentos relacionados

        Forneça:
        1. Conteúdo editado/criado
        2. Sugestões de melhorias
        3. Links internos apropriados
        4. Tags relevantes
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return f"✏️ **Edição de Conteúdo:**\n\n{response.content}"

class SuggestionAgent:
    """Agente para sugestões de melhorias"""

    def __init__(self, llm):
        self.llm = llm

    async def suggest_improvements(self, vaults: List[ObsidianVault], query: str, context: List[Dict]) -> str:
        prompt = f"""
        Baseado nos cofres Obsidian disponíveis, sugira melhorias:

        Consulta: {query}
        Cofres: {[vault.name for vault in vaults]}

        Sugestões para:
        1. Organização de arquivos
        2. Novas notas a criar
        3. Conexões a estabelecer
        4. Tags a adicionar
        5. Estruturas a melhorar
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return f"💡 **Sugestões de Melhorias:**\n\n{response.content}"

class CoordinatorAgent:
    """Agente coordenador que decide qual agente usar"""

    def __init__(self, llm):
        self.llm = llm

    async def decide_agent(self, message: str) -> Dict[str, str]:
        prompt = f"""
        Analise a mensagem do usuário e decida qual agente deve responder:

        Mensagem: "{message}"

        Agentes disponíveis:
        - analyzer: Para análises gerais dos cofres
        - connector: Para encontrar conexões entre notas
        - editor: Para editar/criar conteúdo
        - suggester: Para sugestões de melhorias
        - general: Para chat geral

        Responda apenas com o nome do agente.
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        agent_name = response.content.strip().lower()

        if agent_name not in ["analyzer", "connector", "editor", "suggester"]:
            agent_name = "general"

        return {"agent": agent_name, "reasoning": "Decisão baseada no conteúdo da mensagem"}

# Função principal para demonstração
async def main():
    """Função principal de demonstração"""
    print("🚀 Iniciando Sistema Multi-Agente Obsidian")
    print("=" * 50)

    # Inicializa sistema
    system = ObsidianMultiAgentSystem()

    # Exemplo de configuração de cofres
    # Substitua pelos seus caminhos reais
    example_vaults = [
        ("Direito", "C:/Users/<USER>/Documents/Obsidian/Direito", "Notas sobre direito constitucional e civil"),
        ("Neurociência", "C:/Users/<USER>/Documents/Obsidian/Neurociencia", "Estudos sobre neurociência e psicologia"),
        ("Marketing", "C:/Users/<USER>/Documents/Obsidian/Marketing", "Estratégias e conceitos de marketing"),
    ]

    print("\n📁 Configurando cofres de exemplo...")
    for name, path, desc in example_vaults:
        print(f"   • {name}: {path}")

    print("\n💡 Para usar com seus cofres reais:")
    print("   1. Edite os caminhos em example_vaults")
    print("   2. Execute: python obsidian_multi_agent_system.py")
    print("   3. Use system.add_vault() para adicionar seus cofres")
    print("   4. Execute system.index_all_vaults() para indexar")
    print("   5. Use system.chat() para conversar")

    print("\n✅ Sistema pronto para uso!")

if __name__ == "__main__":
    asyncio.run(main())
