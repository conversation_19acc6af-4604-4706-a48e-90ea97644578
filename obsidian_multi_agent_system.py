#!/usr/bin/env python3
"""
Sistema Multi-Agente para Múltiplos Cofres Obsidian
Baseado em LangChain + Gemini API

Funcionalidades:
- Análise de múltiplos cofres
- Sugestões de conexões
- Edição colaborativa de arquivos
- Chat inteligente contextual
"""

import os
import json
import asyncio
import re
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from collections import Counter

# LangChain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage, SystemMessage
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain.chains import ConversationalRetrievalChain
from langchain.memory import ConversationBufferMemory

# Configuração
GOOGLE_API_KEY = "AIzaSyCq1rBFLFqX04jQ2ejd5Tkbane-lTvIsVU"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

@dataclass
class ObsidianVault:
    """Representa um cofre do Obsidian"""
    name: str
    path: str
    description: str
    files: List[str] = None

    def __post_init__(self):
        if self.files is None:
            self.files = []

@dataclass
class Connection:
    """Representa uma conexão entre notas"""
    source_file: str
    target_file: str
    source_vault: str
    target_vault: str
    connection_type: str
    confidence: float
    description: str

class AssistenteLocalIntegrado:
    """Assistente local integrado que sempre trabalha com Gemini"""

    def __init__(self):
        self.ativo = True
        print("⚡ Assistente Local Integrado: ATIVO")

    def pre_processar_sempre(self, texto: str) -> Dict[str, Any]:
        """Sempre pré-processa antes do Gemini"""
        if not texto:
            return {}

        return {
            'estatisticas': self._contar_elementos(texto),
            'topicos_juridicos': self._extrair_topicos_juridicos(texto),
            'categorias': self._identificar_categorias(texto),
            'artigos_mencionados': self._extrair_artigos(texto),
            'palavras_chave': self._extrair_palavras_chave(texto),
            'estrutura': self._analisar_estrutura(texto)
        }

    def _contar_elementos(self, texto: str) -> Dict[str, int]:
        """Conta elementos básicos"""
        return {
            'caracteres': len(texto),
            'palavras': len(texto.split()),
            'linhas': len(texto.split('\n')),
            'paragrafos': len([p for p in texto.split('\n\n') if p.strip()]),
            'artigos_citados': len(re.findall(r'art\.?\s*\d+', texto.lower())),
            'leis_citadas': len(re.findall(r'lei\s*n?º?\s*\d+', texto.lower()))
        }

    def _extrair_topicos_juridicos(self, texto: str) -> List[str]:
        """Extrai tópicos jurídicos"""
        topicos = [
            'direito penal', 'direito civil', 'direito constitucional',
            'processo penal', 'processo civil', 'direito do trabalho',
            'direito tributário', 'direito administrativo', 'direito empresarial',
            'homicídio', 'furto', 'roubo', 'estelionato', 'contrato',
            'responsabilidade civil', 'danos morais', 'prescrição',
            'princípio', 'jurisprudência', 'súmula', 'precedente',
            'habeas corpus', 'mandado de segurança', 'ação civil pública'
        ]

        texto_lower = texto.lower()
        encontrados = [topico for topico in topicos if topico in texto_lower]
        return encontrados[:8]

    def _identificar_categorias(self, texto: str) -> List[str]:
        """Identifica categorias jurídicas"""
        categorias = {
            'penal': ['crime', 'penal', 'delito', 'pena', 'código penal', 'homicídio', 'furto'],
            'civil': ['civil', 'contrato', 'responsabilidade', 'danos', 'obrigação'],
            'constitucional': ['constituição', 'constitucional', 'direitos fundamentais'],
            'processual': ['processo', 'procedimento', 'recurso', 'sentença', 'apelação'],
            'trabalhista': ['trabalho', 'trabalhista', 'clt', 'empregado', 'salário'],
            'tributário': ['tributo', 'imposto', 'fiscal', 'icms', 'ipi', 'ir'],
            'administrativo': ['administrativo', 'servidor público', 'licitação'],
            'empresarial': ['empresa', 'sociedade', 'falência', 'recuperação judicial'],
            'consumidor': ['consumidor', 'cdc', 'fornecedor', 'produto', 'serviço']
        }

        texto_lower = texto.lower()
        cats_encontradas = []

        for categoria, palavras in categorias.items():
            if any(palavra in texto_lower for palavra in palavras):
                cats_encontradas.append(categoria)

        return cats_encontradas

    def _extrair_artigos(self, texto: str) -> List[str]:
        """Extrai artigos mencionados"""
        padroes = [
            r'art\.?\s*(\d+)',
            r'artigo\s*(\d+)',
            r'art\.?\s*(\d+)º?',
        ]

        artigos = []
        for padrao in padroes:
            matches = re.findall(padrao, texto.lower())
            artigos.extend([f"art. {match}" for match in matches])

        return list(set(artigos))[:5]

    def _extrair_palavras_chave(self, texto: str) -> List[str]:
        """Extrai palavras-chave importantes"""
        palavras_importantes = [
            'direito', 'lei', 'código', 'constituição', 'crime', 'processo',
            'tribunal', 'juiz', 'advogado', 'réu', 'autor', 'sentença',
            'recurso', 'apelação', 'cassação', 'habeas corpus', 'mandado',
            'princípio', 'jurisprudência', 'súmula', 'precedente'
        ]

        texto_lower = texto.lower()
        encontradas = [palavra for palavra in palavras_importantes if palavra in texto_lower]
        return encontradas[:6]

    def _analisar_estrutura(self, texto: str) -> str:
        """Analisa estrutura do documento"""
        linhas = texto.split('\n')

        # Conta títulos/seções
        titulos = len([linha for linha in linhas if linha.startswith('#') or linha.isupper()])
        listas = len([linha for linha in linhas if linha.strip().startswith(('-', '*', '•'))])

        if titulos > 0:
            return f"Documento estruturado: {titulos} seções, {listas} itens de lista"
        else:
            return f"Texto corrido: {len(linhas)} linhas"

class ObsidianMultiAgentSystem:
    """Sistema principal multi-agente para Obsidian"""

    def __init__(self):
        # Assistente Local SEMPRE ativo
        self.assistente_local = AssistenteLocalIntegrado()

        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-pro",
            temperature=0.3,
            google_api_key=GOOGLE_API_KEY
        )

        self.embeddings = GoogleGenerativeAIEmbeddings(
            model="models/embedding-001",
            google_api_key=GOOGLE_API_KEY
        )

        self.vaults: List[ObsidianVault] = []
        self.vectorstore = None
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        )

        # Agentes especializados
        self.agents = {
            "analyzer": VaultAnalyzerAgent(self.llm),
            "connector": ConnectionFinderAgent(self.llm),
            "editor": ContentEditorAgent(self.llm),
            "suggester": SuggestionAgent(self.llm),
            "coordinator": CoordinatorAgent(self.llm)
        }

        print("🤖 Sistema Multi-Agente Obsidian inicializado!")
        print(f"⚡ Assistente Local: SEMPRE ATIVO")
        print(f"✅ Gemini API configurada")
        print(f"✅ {len(self.agents)} agentes especializados carregados")
        print(f"🤝 MODO COLABORATIVO: Local + Gemini SEMPRE juntos!")

    def add_vault(self, name: str, path: str, description: str):
        """Adiciona um cofre ao sistema"""
        vault_path = Path(path)
        if not vault_path.exists():
            print(f"❌ Caminho não encontrado: {path}")
            return False

        vault = ObsidianVault(name, path, description)
        vault.files = self._scan_vault_files(vault_path)
        self.vaults.append(vault)

        print(f"✅ Cofre '{name}' adicionado: {len(vault.files)} arquivos")
        return True

    def _scan_vault_files(self, vault_path: Path) -> List[str]:
        """Escaneia arquivos markdown no cofre"""
        files = []
        for file_path in vault_path.rglob("*.md"):
            if not any(part.startswith('.') for part in file_path.parts):
                files.append(str(file_path))
        return files

    async def index_all_vaults(self):
        """Indexa todos os cofres para busca semântica"""
        print("🔄 Indexando todos os cofres...")

        all_documents = []
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200
        )

        for vault in self.vaults:
            print(f"📁 Processando cofre: {vault.name}")

            for file_path in vault.files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Adiciona metadados
                    metadata = {
                        "source": file_path,
                        "vault": vault.name,
                        "filename": Path(file_path).name
                    }

                    # Divide em chunks
                    chunks = text_splitter.split_text(content)
                    for chunk in chunks:
                        all_documents.append({
                            "content": chunk,
                            "metadata": metadata
                        })

                except Exception as e:
                    print(f"⚠️ Erro ao processar {file_path}: {e}")

        # Cria vectorstore
        texts = [doc["content"] for doc in all_documents]
        metadatas = [doc["metadata"] for doc in all_documents]

        self.vectorstore = FAISS.from_texts(
            texts,
            self.embeddings,
            metadatas=metadatas
        )

        print(f"✅ Indexação concluída: {len(all_documents)} chunks processados")

    async def chat(self, message: str) -> str:
        """Interface de chat com colaboração AUTOMÁTICA Local + Gemini"""
        print(f"💬 Usuário: {message}")

        inicio = time.time()

        # ETAPA 1: Assistente Local SEMPRE pré-processa
        print("⚡ Etapa 1: Assistente Local pré-processando...")

        # Busca contexto relevante
        context = await self._get_relevant_context(message)

        # Combina contexto para análise local
        texto_contexto = "\n".join([ctx["content"] for ctx in context])

        # Pré-processamento AUTOMÁTICO
        analise_local = self.assistente_local.pre_processar_sempre(texto_contexto)

        tempo_local = time.time() - inicio
        print(f"✅ Pré-processamento em {tempo_local:.1f}s")

        # ETAPA 2: Coordenador decide agente Gemini
        agent_decision = await self.agents["coordinator"].decide_agent(message)
        selected_agent = agent_decision["agent"]

        print(f"🎯 Agente Gemini selecionado: {selected_agent}")

        # ETAPA 3: Agente Gemini usa análise local
        if selected_agent == "analyzer":
            response = await self._analyzer_com_local(message, analise_local, context)
        elif selected_agent == "connector":
            response = await self._connector_com_local(message, analise_local, context)
        elif selected_agent == "editor":
            response = await self._editor_com_local(message, analise_local, context)
        elif selected_agent == "suggester":
            response = await self._suggester_com_local(message, analise_local, context)
        else:
            response = await self._general_chat_com_local(message, analise_local, context)

        tempo_total = time.time() - inicio

        # Resposta COLABORATIVA
        return f"""🤝 [COLABORATIVO] Processado em {tempo_total:.1f}s

{response}

---
⚡ Pré-processamento Local: {tempo_local:.1f}s | 🧠 Análise Gemini: {tempo_total - tempo_local:.1f}s
📊 Categorias identificadas: {', '.join(analise_local.get('categorias', []))}
🔍 Tópicos encontrados: {len(analise_local.get('topicos_juridicos', []))} tópicos jurídicos
📄 Artigos mencionados: {', '.join(analise_local.get('artigos_mencionados', []))}
"""

    async def _get_relevant_context(self, query: str, k: int = 5) -> List[Dict]:
        """Busca contexto relevante nos cofres"""
        if not self.vectorstore:
            return []

        docs = self.vectorstore.similarity_search(query, k=k)
        context = []

        for doc in docs:
            context.append({
                "content": doc.page_content,
                "metadata": doc.metadata
            })

        return context

    async def _analyzer_com_local(self, message: str, analise_local: Dict, context: List[Dict]) -> str:
        """Analyzer com pré-processamento local"""
        contexto_enriquecido = self._montar_contexto_colaborativo(analise_local, context)

        prompt = f"""
        {contexto_enriquecido}

        PERGUNTA: {message}

        Como analista especializado, forneça análise detalhada dos cofres considerando:
        1. O pré-processamento local já realizado
        2. Os documentos e categorias identificadas
        3. Seu conhecimento especializado

        Responda de forma estruturada e completa.
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return response.content

    async def _connector_com_local(self, message: str, analise_local: Dict, context: List[Dict]) -> str:
        """Connector com pré-processamento local"""
        contexto_enriquecido = self._montar_contexto_colaborativo(analise_local, context)

        prompt = f"""
        {contexto_enriquecido}

        PERGUNTA: {message}

        Como especialista em conexões, identifique:
        1. Conexões entre os tópicos pré-identificados
        2. Relações entre as categorias encontradas
        3. Links potenciais entre documentos
        4. Sugestões de novas conexões

        Use formato [[Nome da Nota]] para links internos.
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return response.content

    async def _editor_com_local(self, message: str, analise_local: Dict, context: List[Dict]) -> str:
        """Editor com pré-processamento local"""
        contexto_enriquecido = self._montar_contexto_colaborativo(analise_local, context)

        prompt = f"""
        {contexto_enriquecido}

        SOLICITAÇÃO: {message}

        Como editor especializado, considerando a análise prévia:
        1. Use as categorias e tópicos já identificados
        2. Mantenha consistência com a estrutura existente
        3. Incorpore os artigos e palavras-chave relevantes
        4. Forneça conteúdo bem estruturado

        Responda com o conteúdo editado/criado.
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return response.content

    async def _suggester_com_local(self, message: str, analise_local: Dict, context: List[Dict]) -> str:
        """Suggester com pré-processamento local"""
        contexto_enriquecido = self._montar_contexto_colaborativo(analise_local, context)

        prompt = f"""
        {contexto_enriquecido}

        PERGUNTA: {message}

        Como consultor especializado, baseado na análise prévia, sugira:
        1. Melhorias para as categorias identificadas
        2. Organização dos tópicos encontrados
        3. Estruturação dos artigos mencionados
        4. Otimizações gerais do conhecimento

        Forneça sugestões práticas e implementáveis.
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return response.content

    async def _general_chat_com_local(self, message: str, analise_local: Dict, context: List[Dict]) -> str:
        """Chat geral com pré-processamento local"""
        contexto_enriquecido = self._montar_contexto_colaborativo(analise_local, context)

        prompt = f"""
        {contexto_enriquecido}

        PERGUNTA: {message}

        Como assistente inteligente, responda considerando:
        1. A análise prévia realizada
        2. As categorias e tópicos identificados
        3. O contexto dos documentos
        4. Seu conhecimento especializado

        Forneça resposta útil e contextualizada.
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return response.content

    def _montar_contexto_colaborativo(self, analise_local: Dict, context: List[Dict]) -> str:
        """Monta contexto enriquecido com análise local"""
        contexto = []

        # Análise do assistente local
        if analise_local:
            contexto.append("📊 ANÁLISE PRÉVIA (Assistente Local):")

            stats = analise_local.get('estatisticas', {})
            contexto.append(f"- Estatísticas: {stats.get('palavras', 0)} palavras, {stats.get('paragrafos', 0)} parágrafos")

            categorias = analise_local.get('categorias', [])
            if categorias:
                contexto.append(f"- Categorias: {', '.join(categorias)}")

            topicos = analise_local.get('topicos_juridicos', [])
            if topicos:
                contexto.append(f"- Tópicos jurídicos: {', '.join(topicos[:5])}")

            artigos = analise_local.get('artigos_mencionados', [])
            if artigos:
                contexto.append(f"- Artigos citados: {', '.join(artigos)}")

            palavras = analise_local.get('palavras_chave', [])
            if palavras:
                contexto.append(f"- Palavras-chave: {', '.join(palavras)}")

            estrutura = analise_local.get('estrutura', '')
            if estrutura:
                contexto.append(f"- Estrutura: {estrutura}")

            contexto.append("")

        # Documentos relevantes
        if context:
            contexto.append("📄 DOCUMENTOS RELEVANTES:")
            for i, ctx in enumerate(context[:3], 1):
                fonte = ctx.get('metadata', {}).get('filename', f'doc_{i}')
                conteudo = ctx.get('content', '')[:300]
                contexto.append(f"\n{i}. {fonte}:")
                contexto.append(conteudo + "...")
            contexto.append("")

        return "\n".join(contexto)

    def get_vault_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas dos cofres"""
        stats = {
            "total_vaults": len(self.vaults),
            "total_files": sum(len(vault.files) for vault in self.vaults),
            "vaults": []
        }

        for vault in self.vaults:
            vault_stats = {
                "name": vault.name,
                "description": vault.description,
                "file_count": len(vault.files),
                "path": vault.path
            }
            stats["vaults"].append(vault_stats)

        return stats

# Agentes especializados
class VaultAnalyzerAgent:
    """Agente para análise de cofres"""

    def __init__(self, llm):
        self.llm = llm

    async def analyze_vaults(self, vaults: List[ObsidianVault], query: str) -> str:
        vault_info = "\n".join([
            f"- {vault.name}: {len(vault.files)} arquivos ({vault.description})"
            for vault in vaults
        ])

        prompt = f"""
        Analise os seguintes cofres Obsidian:

        {vault_info}

        Consulta: {query}

        Forneça uma análise detalhada incluindo:
        1. Visão geral dos cofres
        2. Padrões identificados
        3. Áreas de conhecimento cobertas
        4. Sugestões de organização
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return f"📊 **Análise dos Cofres:**\n\n{response.content}"

class ConnectionFinderAgent:
    """Agente para encontrar conexões entre notas"""

    def __init__(self, llm):
        self.llm = llm

    async def find_connections(self, vaults: List[ObsidianVault], query: str, context: List[Dict]) -> str:
        context_text = "\n".join([
            f"[{ctx['metadata']['vault']}] {ctx['metadata']['filename']}"
            for ctx in context
        ])

        prompt = f"""
        Encontre conexões entre as seguintes notas de diferentes cofres:

        {context_text}

        Consulta: {query}

        Identifique:
        1. Conexões conceituais
        2. Temas relacionados
        3. Oportunidades de links
        4. Sugestões de novas notas

        Formato: [[Nome da Nota]] para links internos
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return f"🔗 **Conexões Encontradas:**\n\n{response.content}"

class ContentEditorAgent:
    """Agente para edição de conteúdo"""

    def __init__(self, llm):
        self.llm = llm

    async def edit_content(self, request: str, context: List[Dict]) -> str:
        prompt = f"""
        Você é um editor especializado em markdown para Obsidian.

        Solicitação: {request}

        Contexto disponível: {len(context)} documentos relacionados

        Forneça:
        1. Conteúdo editado/criado
        2. Sugestões de melhorias
        3. Links internos apropriados
        4. Tags relevantes
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return f"✏️ **Edição de Conteúdo:**\n\n{response.content}"

class SuggestionAgent:
    """Agente para sugestões de melhorias"""

    def __init__(self, llm):
        self.llm = llm

    async def suggest_improvements(self, vaults: List[ObsidianVault], query: str, context: List[Dict]) -> str:
        prompt = f"""
        Baseado nos cofres Obsidian disponíveis, sugira melhorias:

        Consulta: {query}
        Cofres: {[vault.name for vault in vaults]}

        Sugestões para:
        1. Organização de arquivos
        2. Novas notas a criar
        3. Conexões a estabelecer
        4. Tags a adicionar
        5. Estruturas a melhorar
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return f"💡 **Sugestões de Melhorias:**\n\n{response.content}"

class CoordinatorAgent:
    """Agente coordenador que decide qual agente usar"""

    def __init__(self, llm):
        self.llm = llm

    async def decide_agent(self, message: str) -> Dict[str, str]:
        prompt = f"""
        Analise a mensagem do usuário e decida qual agente deve responder:

        Mensagem: "{message}"

        Agentes disponíveis:
        - analyzer: Para análises gerais dos cofres
        - connector: Para encontrar conexões entre notas
        - editor: Para editar/criar conteúdo
        - suggester: Para sugestões de melhorias
        - general: Para chat geral

        Responda apenas com o nome do agente.
        """

        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        agent_name = response.content.strip().lower()

        if agent_name not in ["analyzer", "connector", "editor", "suggester"]:
            agent_name = "general"

        return {"agent": agent_name, "reasoning": "Decisão baseada no conteúdo da mensagem"}

# Função principal para demonstração
async def main():
    """Função principal de demonstração"""
    print("🚀 Iniciando Sistema Multi-Agente Obsidian")
    print("=" * 50)

    # Inicializa sistema
    system = ObsidianMultiAgentSystem()

    # Configuração dos seus cofres reais do iCloud
    example_vaults = [
        ("Direito Penal", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Direito Penal", "Notas sobre direito penal e processo penal"),
        ("Direito Civil", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\COLEÇÃO DIREITO CIVIL", "Notas sobre direito civil, contratos e obrigações"),
        ("Processo Civil", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\COLEÇÃO DE PROCESSO CIVIL", "Notas sobre processo civil e procedimentos"),
        ("Constitucional", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Constitucional", "Notas sobre direito constitucional"),
        ("Consumidor", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Consumidor", "Notas sobre direito do consumidor"),
        ("Empresarial", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Empresarial", "Notas sobre direito empresarial"),
        ("Trabalho", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Trabalho", "Notas sobre direito do trabalho"),
        ("Tributário", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Tributario", "Notas sobre direito tributário"),
        ("Previdência", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\PREVIDENCIA", "Notas sobre direito previdenciário"),
        ("Direitos Humanos", r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Direitos Humanos", "Notas sobre direitos humanos"),
    ]

    print("\n📁 Configurando cofres de exemplo...")
    for name, path, desc in example_vaults:
        print(f"   • {name}: {path}")

    print("\n💡 Para usar com seus cofres reais:")
    print("   1. Edite os caminhos em example_vaults")
    print("   2. Execute: python obsidian_multi_agent_system.py")
    print("   3. Use system.add_vault() para adicionar seus cofres")
    print("   4. Execute system.index_all_vaults() para indexar")
    print("   5. Use system.chat() para conversar")

    print("\n✅ Sistema pronto para uso!")

if __name__ == "__main__":
    asyncio.run(main())
