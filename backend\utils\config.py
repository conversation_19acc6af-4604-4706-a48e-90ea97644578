import os
from dotenv import load_dotenv
from pathlib import Path

# Carrega variáveis de ambiente
load_dotenv()

class Config:
    """Configurações centralizadas do sistema"""

    # API Keys
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

    # Paths
    OBSIDIAN_VAULT_PATH = Path(os.getenv("OBSIDIAN_VAULT_PATH", "./data/obsidian_notes"))
    DATABASE_PATH = Path(os.getenv("DATABASE_PATH", "./data/memory.db"))
    VECTOR_STORE_PATH = Path(os.getenv("VECTOR_STORE_PATH", "./data/vector_store"))

    # Server
    HOST = os.getenv("HOST", "localhost")
    PORT = int(os.getenv("PORT", 8000))
    DEBUG = os.getenv("DEBUG", "True").lower() == "true"

    # Chat Configuration
    MAX_MEMORY_MESSAGES = int(os.getenv("MAX_MEMORY_MESSAGES", 50))
    CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", 1000))
    CHUNK_OVERLAP = int(os.getenv("CHUNK_OVERLAP", 200))
    MAX_TOKENS_PER_REQUEST = int(os.getenv("MAX_TOKENS_PER_REQUEST", 4000))

    # Models
    EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "sentence-transformers/all-MiniLM-L6-v2")
    GEMINI_MODEL = "gemini-1.5-flash"

    @classmethod
    def ensure_directories(cls):
        """Cria diretórios necessários se não existirem"""
        cls.OBSIDIAN_VAULT_PATH.mkdir(parents=True, exist_ok=True)
        cls.DATABASE_PATH.parent.mkdir(parents=True, exist_ok=True)
        cls.VECTOR_STORE_PATH.mkdir(parents=True, exist_ok=True)

    @classmethod
    def validate_config(cls):
        """Valida configurações essenciais"""
        if not cls.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY não configurada. Verifique o arquivo .env")

        cls.ensure_directories()
        return True

# Instância global de configuração
config = Config()
