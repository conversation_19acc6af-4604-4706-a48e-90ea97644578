#!/usr/bin/env python3
"""
Sistema Híbrido Simples: Gemini + Transformers Local
Versão simplificada que funciona offline
"""

import asyncio
import json
import os
from pathlib import Path
from typing import Optional

# Configuração
GOOGLE_API_KEY = "AIzaSyCq1rBFLFqX04jQ2ejd5Tkbane-lTvIsVU"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

class LocalLLM:
    """LLM local usando Transformers"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.loaded = False
        self.load_model()
    
    def load_model(self):
        """Carrega modelo local pequeno"""
        try:
            print("🔄 Carregando modelo local...")
            
            from transformers import AutoTokenizer, AutoModelForCausalLM
            import torch
            
            # Modelo pequeno e rápido
            model_name = "microsoft/DialoGPT-small"
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(model_name)
            
            # Adiciona pad token se não existir
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            self.loaded = True
            print("✅ Modelo local carregado!")
            
        except Exception as e:
            print(f"❌ Erro ao carregar modelo local: {e}")
            self.loaded = False
    
    def generate_response(self, prompt: str, max_length: int = 100) -> str:
        """Gera resposta usando modelo local"""
        if not self.loaded:
            return "❌ Modelo local não disponível"
        
        try:
            # Prepara prompt em português
            formatted_prompt = f"Usuário: {prompt}\nAssistente:"
            
            # Tokeniza
            inputs = self.tokenizer.encode(formatted_prompt, return_tensors="pt")
            
            # Gera resposta
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs,
                    max_length=inputs.shape[1] + max_length,
                    num_return_sequences=1,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id
                )
            
            # Decodifica
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extrai apenas a resposta do assistente
            if "Assistente:" in response:
                response = response.split("Assistente:")[-1].strip()
            
            return response[:200]  # Limita tamanho
            
        except Exception as e:
            return f"❌ Erro na geração: {e}"

class GeminiLLM:
    """Interface para Gemini"""
    
    def __init__(self):
        self.llm = None
        self.loaded = False
        self.load_model()
    
    def load_model(self):
        """Carrega Gemini"""
        try:
            from langchain_google_genai import ChatGoogleGenerativeAI
            
            self.llm = ChatGoogleGenerativeAI(
                model="gemini-1.5-pro",
                temperature=0.3,
                google_api_key=GOOGLE_API_KEY
            )
            
            self.loaded = True
            print("✅ Gemini carregado!")
            
        except Exception as e:
            print(f"❌ Erro ao carregar Gemini: {e}")
            self.loaded = False
    
    async def generate_response(self, prompt: str) -> str:
        """Gera resposta usando Gemini"""
        if not self.loaded:
            return "❌ Gemini não disponível"
        
        try:
            from langchain.schema import HumanMessage
            
            response = await self.llm.ainvoke([HumanMessage(content=prompt)])
            return response.content
            
        except Exception as e:
            return f"❌ Erro no Gemini: {e}"

class SistemaHibridoSimples:
    """Sistema híbrido simplificado"""
    
    def __init__(self):
        print("🤖 SISTEMA HÍBRIDO SIMPLES")
        print("=" * 30)
        
        # Carrega LLMs
        self.gemini = GeminiLLM()
        self.local = LocalLLM()
        
        # Status
        print(f"🌐 Gemini: {'✅ Ativo' if self.gemini.loaded else '❌ Inativo'}")
        print(f"🏠 Local: {'✅ Ativo' if self.local.loaded else '❌ Inativo'}")
        
        if not self.gemini.loaded and not self.local.loaded:
            print("❌ Nenhum LLM disponível!")
        else:
            print("✅ Sistema híbrido pronto!")
    
    def choose_llm(self, query: str) -> str:
        """Escolhe qual LLM usar"""
        
        # Critérios para usar local
        use_local = (
            len(query) < 50 or  # Queries curtas
            any(word in query.lower() for word in [
                "oi", "olá", "tchau", "obrigado", "sim", "não"
            ])
        )
        
        # Se tem local e atende critérios
        if self.local.loaded and use_local:
            return "local"
        
        # Senão usa Gemini (se disponível)
        if self.gemini.loaded:
            return "gemini"
        
        # Fallback para local
        if self.local.loaded:
            return "local"
        
        return "none"
    
    async def chat(self, query: str) -> str:
        """Chat híbrido"""
        print(f"💬 Usuário: {query}")
        
        # Escolhe LLM
        chosen = self.choose_llm(query)
        print(f"🎯 Usando: {chosen}")
        
        if chosen == "gemini":
            response = await self.gemini.generate_response(query)
            return f"🌐 [Gemini] {response}"
        
        elif chosen == "local":
            response = self.local.generate_response(query)
            return f"🏠 [Local] {response}"
        
        else:
            return "❌ Nenhum LLM disponível"

async def demo_sistema():
    """Demonstração do sistema"""
    print("🎯 DEMO SISTEMA HÍBRIDO")
    print("=" * 30)
    
    # Inicializa
    sistema = SistemaHibridoSimples()
    
    # Perguntas de teste
    perguntas = [
        "Olá!",
        "O que é direito penal?",
        "Obrigado",
        "Explique contratos de forma simples",
        "Tchau!"
    ]
    
    for pergunta in perguntas:
        print(f"\n❓ {pergunta}")
        resposta = await sistema.chat(pergunta)
        print(f"🤖 {resposta}")
        print("-" * 30)

async def chat_interativo():
    """Chat interativo"""
    print("💬 CHAT INTERATIVO")
    print("=" * 20)
    print("Digite 'sair' para terminar")
    
    sistema = SistemaHibridoSimples()
    
    while True:
        try:
            pergunta = input("\n💬 Você: ").strip()
            
            if pergunta.lower() in ['sair', 'exit', 'quit']:
                print("👋 Até logo!")
                break
            
            if not pergunta:
                continue
            
            resposta = await sistema.chat(pergunta)
            print(f"🤖 {resposta}")
            
        except KeyboardInterrupt:
            print("\n👋 Chat encerrado")
            break
        except Exception as e:
            print(f"❌ Erro: {e}")

def testar_cofres():
    """Testa carregamento de cofres"""
    print("📁 TESTANDO COFRES")
    print("=" * 20)
    
    cofres_teste = [
        r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Direito Penal",
        r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Constitucional",
        r"C:\Users\<USER>\iCloudDrive\iCloud~md~obsidian\IPAD\Consumidor"
    ]
    
    for cofre in cofres_teste:
        path = Path(cofre)
        if path.exists():
            md_files = list(path.rglob("*.md"))
            print(f"✅ {path.name}: {len(md_files)} arquivos")
        else:
            print(f"❌ {path.name}: não encontrado")

async def main():
    """Menu principal"""
    print("🤖 SISTEMA HÍBRIDO OBSIDIAN")
    print("=" * 40)
    print("Gemini + LLM Local para máxima eficiência")
    print()
    
    print("Escolha uma opção:")
    print("1. 🧪 Demo do sistema")
    print("2. 💬 Chat interativo")
    print("3. 📁 Testar cofres")
    print("4. 🌐 Interface web")
    
    try:
        choice = input("\nOpção (1-4): ").strip()
        
        if choice == "1":
            await demo_sistema()
        elif choice == "2":
            await chat_interativo()
        elif choice == "3":
            testar_cofres()
        elif choice == "4":
            print("🌐 Abrindo interface web...")
            import subprocess
            subprocess.run(["streamlit", "run", "web_interface.py"])
        else:
            print("❌ Opção inválida")
            
    except KeyboardInterrupt:
        print("\n👋 Programa encerrado")
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    asyncio.run(main())
